{
  "iteration": 1,
  "results": [
    {
      "params": {
        "w1_dangerous_weight": 0.16348556464509598,
        "w2_progressive_weight": 0.5044436464098068,
        "w3_clarification_weight": 0.3320707889450971,
        "gamma": 0.5952261326291828,
        "beta": 0.503099145834358,
        "reject_threshold": 0.6013293782682286,
        "clarify_threshold": 0.4786026384096656,
        "delta1_evasion_penalty": 0.34268965255126893,
        "delta2_single_attack_penalty": 0.19868430664430417,
        "delta3_behavioral_drift_penalty": 0.1641589676574594,
        "high_escalation_threshold": 0.7587439175636462,
        "moderate_escalation_threshold": 0.49374453033180027,
        "low_credibility_threshold": 0.31329627015279005,
        "high_credibility_threshold": 0.5466079609818781,
        "credibility_penalty_weight": 0.2816777499220472,
        "trajectory_penalty_weight": 0.15096759773376067,
        "eta_optimization_factor": 0.9346267734024977,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6501740895618783,
          0.09046284059389165,
          0.2593630698442301
        ]
      },
      "f1_score": 0.7255102040816326,
      "f2_score": 0.0,
      "f3_score": 0.025510204081632654,
      "overall_score": 0.1270816389656027,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.44696393295849024,
        "w2_progressive_weight": 0.2007264844757853,
        "w3_clarification_weight": 0.3523095825657245,
        "gamma": 0.665336276822851,
        "beta": 0.30620744651233944,
        "reject_threshold": 0.7222329678883243,
        "clarify_threshold": 0.3021344383878287,
        "delta1_evasion_penalty": 0.20798566370344693,
        "delta2_single_attack_penalty": 0.41523500862301155,
        "delta3_behavioral_drift_penalty": 0.2217673028523699,
        "high_escalation_threshold": 0.7477409303532572,
        "moderate_escalation_threshold": 0.3265947979240638,
        "low_credibility_threshold": 0.35516963831451764,
        "high_credibility_threshold": 0.6815686162974268,
        "credibility_penalty_weight": 0.2817896405759499,
        "trajectory_penalty_weight": 0.14195719509028815,
        "eta_optimization_factor": 1.0878455715056634,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.6621269843377349,
          0.22179864009233374,
          0.11607437556993143
        ]
      },
      "f1_score": 0.44693877551020406,
      "f2_score": 0.0,
      "f3_score": 0.3979591836734694,
      "overall_score": 0.3399703467718891,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.08804266395811279,
        "w2_progressive_weight": 0.4014967072268864,
        "w3_clarification_weight": 0.5104606288150009,
        "gamma": 0.5033463384005714,
        "beta": 0.3890358981313907,
        "reject_threshold": 0.8979691519467452,
        "clarify_threshold": 0.6148513761274659,
        "delta1_evasion_penalty": 0.16331174990445274,
        "delta2_single_attack_penalty": 0.26476464072832695,
        "delta3_behavioral_drift_penalty": 0.28923421621362233,
        "high_escalation_threshold": 0.6391046950060586,
        "moderate_escalation_threshold": 0.3318798786473663,
        "low_credibility_threshold": 0.28752396101058386,
        "high_credibility_threshold": 0.6267710908237699,
        "credibility_penalty_weight": 0.11810564631224854,
        "trajectory_penalty_weight": 0.17728165832365378,
        "eta_optimization_factor": 0.816963598622944,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.35948230904681794,
          0.3643957030747676,
          0.2761219878784144
        ]
      },
      "f1_score": 0.20816326530612245,
      "f2_score": 0.05306122448979592,
      "f3_score": 0.5459183673469388,
      "overall_score": 0.3183009884074271,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.19380891101644573,
        "w2_progressive_weight": 0.32560749551569634,
        "w3_clarification_weight": 0.48058359346785795,
        "gamma": 0.6211475299391335,
        "beta": 0.3371198969305595,
        "reject_threshold": 0.6292543720286127,
        "clarify_threshold": 0.5270918360168224,
        "delta1_evasion_penalty": 0.19855823516928656,
        "delta2_single_attack_penalty": 0.27801946141047273,
        "delta3_behavioral_drift_penalty": 0.24420133333478045,
        "high_escalation_threshold": 0.7002849296653142,
        "moderate_escalation_threshold": 0.45882117834876635,
        "low_credibility_threshold": 0.38576429133582335,
        "high_credibility_threshold": 0.6394119922347338,
        "credibility_penalty_weight": 0.10190279131497298,
        "trajectory_penalty_weight": 0.07236444583305535,
        "eta_optimization_factor": 0.8868977219200479,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.4039525298988244,
          0.25931910022403404,
          0.33672836987714155
        ]
      },
      "f1_score": 0.5704081632653061,
      "f2_score": 0.0,
      "f3_score": 0.16326530612244897,
      "overall_score": 0.18901281246229634,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.45554118627581924,
        "w2_progressive_weight": 0.10717757958464989,
        "w3_clarification_weight": 0.4372812341395308,
        "gamma": 0.7215029157273358,
        "beta": 0.7547773267933483,
        "reject_threshold": 0.625413171155009,
        "clarify_threshold": 0.3904243698854563,
        "delta1_evasion_penalty": 0.3090186387565601,
        "delta2_single_attack_penalty": 0.21499640671582593,
        "delta3_behavioral_drift_penalty": 0.08994839665011688,
        "high_escalation_threshold": 0.6715753192055259,
        "moderate_escalation_threshold": 0.3696144423650936,
        "low_credibility_threshold": 0.39984316375647055,
        "high_credibility_threshold": 0.6382907041264148,
        "credibility_penalty_weight": 0.29222557286152473,
        "trajectory_penalty_weight": 0.14014966232290238,
        "eta_optimization_factor": 1.0328348760127908,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6039532505140782,
          0.28778770634884454,
          0.10825904313707711
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.3429572405212991,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.09974892727294891,
        "w2_progressive_weight": 0.3503308409165093,
        "w3_clarification_weight": 0.5499202318105418,
        "gamma": 0.7165452752758581,
        "beta": 0.6206264787484028,
        "reject_threshold": 0.6477355418756896,
        "clarify_threshold": 0.3656332033907702,
        "delta1_evasion_penalty": 0.26964769973414654,
        "delta2_single_attack_penalty": 0.250517475930953,
        "delta3_behavioral_drift_penalty": 0.19002599647055307,
        "high_escalation_threshold": 0.6976200095446279,
        "moderate_escalation_threshold": 0.4395827516738746,
        "low_credibility_threshold": 0.3622451548518877,
        "high_credibility_threshold": 0.5696436179675057,
        "credibility_penalty_weight": 0.20601261581117875,
        "trajectory_penalty_weight": 0.16651111686608805,
        "eta_optimization_factor": 0.9375751443970246,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6039997789982564,
          0.2066369778886193,
          0.1893632431131243
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.07741384168380026,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.22106503669586378,
        "w2_progressive_weight": 0.4531120233103865,
        "w3_clarification_weight": 0.32582293999374984,
        "gamma": 0.876137586124695,
        "beta": 0.5533968334973436,
        "reject_threshold": 0.7153847596331737,
        "clarify_threshold": 0.5506508292555287,
        "delta1_evasion_penalty": 0.13611691899920175,
        "delta2_single_attack_penalty": 0.1218204348244429,
        "delta3_behavioral_drift_penalty": 0.07421456380770185,
        "high_escalation_threshold": 0.6437557414204269,
        "moderate_escalation_threshold": 0.3076066022021594,
        "low_credibility_threshold": 0.20939863029589423,
        "high_credibility_threshold": 0.5746647532849154,
        "credibility_penalty_weight": 0.1335735072136129,
        "trajectory_penalty_weight": 0.12354448612156471,
        "eta_optimization_factor": 0.813641612773747,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.4270329363919537,
          0.26791321948936386,
          0.30505384411868247
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.16700998632452743,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3003976930754073,
        "w2_progressive_weight": 0.46169059187457934,
        "w3_clarification_weight": 0.23791171505001324,
        "gamma": 0.805186507909424,
        "beta": 0.5893395755765678,
        "reject_threshold": 0.7545132108974619,
        "clarify_threshold": 0.3134505418147607,
        "delta1_evasion_penalty": 0.11614254014061537,
        "delta2_single_attack_penalty": 0.36591630977900236,
        "delta3_behavioral_drift_penalty": 0.13113064705507832,
        "high_escalation_threshold": 0.5159238355993897,
        "moderate_escalation_threshold": 0.4496960255071474,
        "low_credibility_threshold": 0.24986418637520313,
        "high_credibility_threshold": 0.6111187779058905,
        "credibility_penalty_weight": 0.19240424587093627,
        "trajectory_penalty_weight": 0.12597261270316795,
        "eta_optimization_factor": 0.9362711135698304,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6404328674198231,
          0.22753609122020635,
          0.13203104135997062
        ]
      },
      "f1_score": 0.713265306122449,
      "f2_score": 0.0,
      "f3_score": 0.05102040816326531,
      "overall_score": 0.22640160531858203,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.2728281012589285,
        "w2_progressive_weight": 0.3630960565376204,
        "w3_clarification_weight": 0.3640758422034511,
        "gamma": 0.8800922994035436,
        "beta": 0.34695704293494073,
        "reject_threshold": 0.7755731908022738,
        "clarify_threshold": 0.30294468892444015,
        "delta1_evasion_penalty": 0.358389285361582,
        "delta2_single_attack_penalty": 0.3272447825383058,
        "delta3_behavioral_drift_penalty": 0.19850601169362653,
        "high_escalation_threshold": 0.5822152812319071,
        "moderate_escalation_threshold": 0.46094476478629204,
        "low_credibility_threshold": 0.212925252487997,
        "high_credibility_threshold": 0.7667958382771559,
        "credibility_penalty_weight": 0.267619474499581,
        "trajectory_penalty_weight": 0.16273984083790988,
        "eta_optimization_factor": 1.1220666556287457,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.4844146611894271,
          0.23871158423348032,
          0.2768737545770926
        ]
      },
      "f1_score": 0.6122448979591837,
      "f2_score": 0.0,
      "f3_score": 0.1836734693877551,
      "overall_score": 0.23390868607344723,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.28185782336443055,
        "w2_progressive_weight": 0.542365637206356,
        "w3_clarification_weight": 0.17577653942921348,
        "gamma": 0.7794087744238244,
        "beta": 0.4919869876632061,
        "reject_threshold": 0.704878735072355,
        "clarify_threshold": 0.40459064082830176,
        "delta1_evasion_penalty": 0.293297624995658,
        "delta2_single_attack_penalty": 0.13729560726342235,
        "delta3_behavioral_drift_penalty": 0.2700017373772965,
        "high_escalation_threshold": 0.5951856195358889,
        "moderate_escalation_threshold": 0.523869676616974,
        "low_credibility_threshold": 0.38076837760221854,
        "high_credibility_threshold": 0.5073574992735432,
        "credibility_penalty_weight": 0.25572462985434785,
        "trajectory_penalty_weight": 0.12477417563295451,
        "eta_optimization_factor": 1.1993126216440122,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5227580903988682,
          0.1861497699290607,
          0.29109213967207104
        ]
      },
      "f1_score": 0.6979591836734693,
      "f2_score": 0.0,
      "f3_score": 0.07142857142857142,
      "overall_score": 0.20928072340950554,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.36249745905447534,
        "w2_progressive_weight": 0.18418263759602344,
        "w3_clarification_weight": 0.4533199033495013,
        "gamma": 0.5327998430735146,
        "beta": 0.64761010556145,
        "reject_threshold": 0.862399574045082,
        "clarify_threshold": 0.41294652400176607,
        "delta1_evasion_penalty": 0.20793731069545837,
        "delta2_single_attack_penalty": 0.24141007501745346,
        "delta3_behavioral_drift_penalty": 0.2094748458446848,
        "high_escalation_threshold": 0.5938982448745119,
        "moderate_escalation_threshold": 0.49794511671783404,
        "low_credibility_threshold": 0.2159609252707121,
        "high_credibility_threshold": 0.550602881440015,
        "credibility_penalty_weight": 0.22472408391534948,
        "trajectory_penalty_weight": 0.06962869359129985,
        "eta_optimization_factor": 1.042546588958711,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.5944103920110307,
          0.2839976942303595,
          0.12159191375860981
        ]
      },
      "f1_score": 0.4887755102040816,
      "f2_score": 0.00510204081632653,
      "f3_score": 0.3622448979591837,
      "overall_score": 0.3423324099634151,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.44018249333149223,
        "w2_progressive_weight": 0.29205121306001075,
        "w3_clarification_weight": 0.2677662936084971,
        "gamma": 0.7105822173204245,
        "beta": 0.7923937501937461,
        "reject_threshold": 0.6388630309910117,
        "clarify_threshold": 0.4304739039222159,
        "delta1_evasion_penalty": 0.20744153824963063,
        "delta2_single_attack_penalty": 0.24042032528680923,
        "delta3_behavioral_drift_penalty": 0.06173577669078782,
        "high_escalation_threshold": 0.7131202611674832,
        "moderate_escalation_threshold": 0.4903970248512766,
        "low_credibility_threshold": 0.28445625132542735,
        "high_credibility_threshold": 0.7392069541356953,
        "credibility_penalty_weight": 0.14011965469568524,
        "trajectory_penalty_weight": 0.11197358411316567,
        "eta_optimization_factor": 0.8177990302827267,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6135370800393278,
          0.23113104178135996,
          0.1553318781793122
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.3306046929388023,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.25776642973413993,
        "w2_progressive_weight": 0.32333751278701134,
        "w3_clarification_weight": 0.4188960574788487,
        "gamma": 0.5607394553190208,
        "beta": 0.6237801298482131,
        "reject_threshold": 0.6625013682305182,
        "clarify_threshold": 0.4949525824324713,
        "delta1_evasion_penalty": 0.2758949037737798,
        "delta2_single_attack_penalty": 0.11362508286700579,
        "delta3_behavioral_drift_penalty": 0.2898371411823932,
        "high_escalation_threshold": 0.6194556896625425,
        "moderate_escalation_threshold": 0.4568399606710125,
        "low_credibility_threshold": 0.23337075921625605,
        "high_credibility_threshold": 0.50508536659288,
        "credibility_penalty_weight": 0.23867704413048074,
        "trajectory_penalty_weight": 0.059000019721895614,
        "eta_optimization_factor": 0.9334043059187946,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.4907763727093223,
          0.34036065975298807,
          0.1688629675376897
        ]
      },
      "f1_score": 0.6969387755102041,
      "f2_score": 0.0,
      "f3_score": 0.0663265306122449,
      "overall_score": 0.20743134208626812,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.4714690568497281,
        "w2_progressive_weight": 0.09303250230123064,
        "w3_clarification_weight": 0.43549844084904116,
        "gamma": 0.807194051946779,
        "beta": 0.7091169293782752,
        "reject_threshold": 0.7476905833996685,
        "clarify_threshold": 0.500177571388234,
        "delta1_evasion_penalty": 0.12963569949117204,
        "delta2_single_attack_penalty": 0.30490755057791535,
        "delta3_behavioral_drift_penalty": 0.25816886374391684,
        "high_escalation_threshold": 0.6928762636056096,
        "moderate_escalation_threshold": 0.5217678979024004,
        "low_credibility_threshold": 0.3909553430826191,
        "high_credibility_threshold": 0.6870737504429241,
        "credibility_penalty_weight": 0.29863044008953854,
        "trajectory_penalty_weight": 0.07045045820031082,
        "eta_optimization_factor": 1.1206499796640732,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.4087974110904113,
          0.348790351860575,
          0.24241223704901374
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.3548615417092815,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.2975968644309657,
        "w2_progressive_weight": 0.35076726533132635,
        "w3_clarification_weight": 0.35163587023770787,
        "gamma": 0.6561633993500482,
        "beta": 0.3373113914499583,
        "reject_threshold": 0.8308661322579136,
        "clarify_threshold": 0.6349947192371359,
        "delta1_evasion_penalty": 0.36697841259941144,
        "delta2_single_attack_penalty": 0.3107893657140293,
        "delta3_behavioral_drift_penalty": 0.20757914820968187,
        "high_escalation_threshold": 0.6773515191461488,
        "moderate_escalation_threshold": 0.3346314075050586,
        "low_credibility_threshold": 0.20553349345903604,
        "high_credibility_threshold": 0.6127948289879184,
        "credibility_penalty_weight": 0.12274093181254936,
        "trajectory_penalty_weight": 0.19607054768262222,
        "eta_optimization_factor": 1.0050077282570273,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.597272953784698,
          0.2675737167158353,
          0.1351533294994667
        ]
      },
      "f1_score": 0.3948979591836735,
      "f2_score": 0.006122448979591836,
      "f3_score": 0.2602040816326531,
      "overall_score": 0.21116503779325224,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3257511103177841,
        "w2_progressive_weight": 0.18552098097274594,
        "w3_clarification_weight": 0.48872790870947,
        "gamma": 0.589315830565666,
        "beta": 0.6036499072914516,
        "reject_threshold": 0.7718362633732356,
        "clarify_threshold": 0.3164509127331873,
        "delta1_evasion_penalty": 0.14766382385554405,
        "delta2_single_attack_penalty": 0.3360579850658264,
        "delta3_behavioral_drift_penalty": 0.22106545649126358,
        "high_escalation_threshold": 0.6189001300859502,
        "moderate_escalation_threshold": 0.5068219780974237,
        "low_credibility_threshold": 0.2672533515481563,
        "high_credibility_threshold": 0.7912736838515474,
        "credibility_penalty_weight": 0.2755958399952949,
        "trajectory_penalty_weight": 0.08275685785070315,
        "eta_optimization_factor": 1.05976516973717,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.3635537295831592,
          0.436813600481562,
          0.1996326699352789
        ]
      },
      "f1_score": 0.5846938775510204,
      "f2_score": 0.0,
      "f3_score": 0.2193877551020408,
      "overall_score": 0.29768559855574117,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.30686979401393255,
        "w2_progressive_weight": 0.3334326079057761,
        "w3_clarification_weight": 0.3596975980802913,
        "gamma": 0.8075538220056162,
        "beta": 0.79953634824046,
        "reject_threshold": 0.7945399242198082,
        "clarify_threshold": 0.6945399242198083,
        "delta1_evasion_penalty": 0.3242270996648027,
        "delta2_single_attack_penalty": 0.21115192498255758,
        "delta3_behavioral_drift_penalty": 0.21978673652455405,
        "high_escalation_threshold": 0.6768010801638787,
        "moderate_escalation_threshold": 0.5768010801638788,
        "low_credibility_threshold": 0.3290136265052399,
        "high_credibility_threshold": 0.7858334146807346,
        "credibility_penalty_weight": 0.24918701362755752,
        "trajectory_penalty_weight": 0.2074266253939847,
        "eta_optimization_factor": 0.9999601438477077,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6293808216270849,
          0.1565216659175492,
          0.21409751245536598
        ]
      },
      "f1_score": 0.7448979591836735,
      "f2_score": 0.0,
      "f3_score": 0.0,
      "overall_score": 0.22858668329609264,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.39040285831464644,
        "w2_progressive_weight": 0.1918778740340003,
        "w3_clarification_weight": 0.4177192676513533,
        "gamma": 0.678419223881397,
        "beta": 0.4390417453105052,
        "reject_threshold": 0.6600286919259953,
        "clarify_threshold": 0.6249419969947276,
        "delta1_evasion_penalty": 0.3617730491097617,
        "delta2_single_attack_penalty": 0.13042609177805714,
        "delta3_behavioral_drift_penalty": 0.11385368632062941,
        "high_escalation_threshold": 0.6876602423463464,
        "moderate_escalation_threshold": 0.5876602423463464,
        "low_credibility_threshold": 0.33157527009390786,
        "high_credibility_threshold": 0.641732990773356,
        "credibility_penalty_weight": 0.1740535142176361,
        "trajectory_penalty_weight": 0.11584156502476498,
        "eta_optimization_factor": 1.0131083840401112,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.47190124761047386,
          0.1746319850015764,
          0.35346676738794974
        ]
      },
      "f1_score": 0.6489795918367347,
      "f2_score": 0.0,
      "f3_score": 0.05612244897959183,
      "overall_score": 0.27680691592748935,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3668421441662303,
        "w2_progressive_weight": 0.4626136405135673,
        "w3_clarification_weight": 0.17054421532020256,
        "gamma": 0.801082202293471,
        "beta": 0.6789600276142532,
        "reject_threshold": 0.8744657644321396,
        "clarify_threshold": 0.31576243596059633,
        "delta1_evasion_penalty": 0.3478216625222661,
        "delta2_single_attack_penalty": 0.4886787657606577,
        "delta3_behavioral_drift_penalty": 0.2722579958170542,
        "high_escalation_threshold": 0.7856681175640017,
        "moderate_escalation_threshold": 0.3434679196802067,
        "low_credibility_threshold": 0.2484064906005889,
        "high_credibility_threshold": 0.7387069532600571,
        "credibility_penalty_weight": 0.11745273403452859,
        "trajectory_penalty_weight": 0.1862571093491308,
        "eta_optimization_factor": 0.9354534186219199,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.4958550141744958,
          0.19978796843614996,
          0.30435701738935433
        ]
      },
      "f1_score": 0.6795918367346939,
      "f2_score": 0.0,
      "f3_score": 0.10204081632653061,
      "overall_score": 0.2667053974966629,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3992023716062095,
        "w2_progressive_weight": 0.28387306524171463,
        "w3_clarification_weight": 0.3169245631520758,
        "gamma": 0.6737931212035001,
        "beta": 0.5588217648591489,
        "reject_threshold": 0.8110721055174265,
        "clarify_threshold": 0.383460882263614,
        "delta1_evasion_penalty": 0.1754021507387251,
        "delta2_single_attack_penalty": 0.2981597726920805,
        "delta3_behavioral_drift_penalty": 0.07066349976253512,
        "high_escalation_threshold": 0.6387379826245752,
        "moderate_escalation_threshold": 0.581614145324032,
        "low_credibility_threshold": 0.25686932266121015,
        "high_credibility_threshold": 0.57346624984721,
        "credibility_penalty_weight": 0.21618732286380657,
        "trajectory_penalty_weight": 0.23493788964030088,
        "eta_optimization_factor": 1.0909741968366449,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5526407854819818,
          0.2284589324464642,
          0.21890028207155393
        ]
      },
      "f1_score": 0.5959183673469388,
      "f2_score": 0.0,
      "f3_score": 0.20918367346938777,
      "overall_score": 0.3041874698614305,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.16331614062634534,
        "w2_progressive_weight": 0.38192024123450796,
        "w3_clarification_weight": 0.45476361813914673,
        "gamma": 0.8295783255428795,
        "beta": 0.3457245961489291,
        "reject_threshold": 0.8768789177560616,
        "clarify_threshold": 0.5503350509303845,
        "delta1_evasion_penalty": 0.20804164898787833,
        "delta2_single_attack_penalty": 0.14660807459599892,
        "delta3_behavioral_drift_penalty": 0.13468169598796143,
        "high_escalation_threshold": 0.7711497267033688,
        "moderate_escalation_threshold": 0.589271765968367,
        "low_credibility_threshold": 0.26734789216483046,
        "high_credibility_threshold": 0.7875038372552753,
        "credibility_penalty_weight": 0.12047835575373828,
        "trajectory_penalty_weight": 0.12542107564368835,
        "eta_optimization_factor": 0.9984749804636341,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.789272244636858,
          0.08368793922597516,
          0.12703981613716675
        ]
      },
      "f1_score": 0.463265306122449,
      "f2_score": 0.0,
      "f3_score": 0.41836734693877553,
      "overall_score": 0.265916950287154,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.19214621429327008,
        "w2_progressive_weight": 0.37958665781582723,
        "w3_clarification_weight": 0.42826712789090265,
        "gamma": 0.5550786652233629,
        "beta": 0.598199614748558,
        "reject_threshold": 0.6467998728260287,
        "clarify_threshold": 0.44748868990912544,
        "delta1_evasion_penalty": 0.1454531552733883,
        "delta2_single_attack_penalty": 0.2649434230743798,
        "delta3_behavioral_drift_penalty": 0.09067314601662459,
        "high_escalation_threshold": 0.6033923302781941,
        "moderate_escalation_threshold": 0.5896696644372346,
        "low_credibility_threshold": 0.3677139381827501,
        "high_credibility_threshold": 0.5547411045882215,
        "credibility_penalty_weight": 0.22533441713161817,
        "trajectory_penalty_weight": 0.24233417639563654,
        "eta_optimization_factor": 1.0070821311890028,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.5932844289026669,
          0.24846248914352415,
          0.158253081953809
        ]
      },
      "f1_score": 0.7153061224489796,
      "f2_score": 0.0,
      "f3_score": 0.04591836734693878,
      "overall_score": 0.1571086907904826,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.532112753036245,
        "w2_progressive_weight": 0.3453825694364779,
        "w3_clarification_weight": 0.12250467752727703,
        "gamma": 0.7417085417097559,
        "beta": 0.791277697882652,
        "reject_threshold": 0.7981672072973468,
        "clarify_threshold": 0.4423159254148945,
        "delta1_evasion_penalty": 0.1418983376577761,
        "delta2_single_attack_penalty": 0.2813495969697799,
        "delta3_behavioral_drift_penalty": 0.15704379417652453,
        "high_escalation_threshold": 0.642510635289425,
        "moderate_escalation_threshold": 0.47185874724504806,
        "low_credibility_threshold": 0.3208850607571621,
        "high_credibility_threshold": 0.7057733592589975,
        "credibility_penalty_weight": 0.164263894239067,
        "trajectory_penalty_weight": 0.1763048657791762,
        "eta_optimization_factor": 1.1790048931761916,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.6272663767168165,
          0.09588982518425347,
          0.27684379809893006
        ]
      },
      "f1_score": 0.7408163265306124,
      "f2_score": 0.0,
      "f3_score": 0.015306122448979591,
      "overall_score": 0.3960728865992072,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.24300293769358014,
        "w2_progressive_weight": 0.4238575776600865,
        "w3_clarification_weight": 0.3331394846463333,
        "gamma": 0.6215044578058668,
        "beta": 0.7833969363408118,
        "reject_threshold": 0.839612356365762,
        "clarify_threshold": 0.5566688322085815,
        "delta1_evasion_penalty": 0.2150039519006373,
        "delta2_single_attack_penalty": 0.3115034628515999,
        "delta3_behavioral_drift_penalty": 0.16159637060440862,
        "high_escalation_threshold": 0.604315767800484,
        "moderate_escalation_threshold": 0.5514161631136858,
        "low_credibility_threshold": 0.21839791486343163,
        "high_credibility_threshold": 0.7417609267797439,
        "credibility_penalty_weight": 0.17312137505633818,
        "trajectory_penalty_weight": 0.21185107998080782,
        "eta_optimization_factor": 0.98808059308173,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.3218871900286672,
          0.4169981030317852,
          0.2611147069395476
        ]
      },
      "f1_score": 0.6561224489795918,
      "f2_score": 0.0,
      "f3_score": 0.12755102040816327,
      "overall_score": 0.20193196379363643,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.24353340277904345,
        "w2_progressive_weight": 0.26472165768857564,
        "w3_clarification_weight": 0.49174493953238096,
        "gamma": 0.9314637586781854,
        "beta": 0.3531129691412251,
        "reject_threshold": 0.8364322148998097,
        "clarify_threshold": 0.48790925339784535,
        "delta1_evasion_penalty": 0.30800041107338183,
        "delta2_single_attack_penalty": 0.14199082262971313,
        "delta3_behavioral_drift_penalty": 0.06959124555580508,
        "high_escalation_threshold": 0.5892278107949762,
        "moderate_escalation_threshold": 0.32758850790247224,
        "low_credibility_threshold": 0.3425657142210879,
        "high_credibility_threshold": 0.6413529146295937,
        "credibility_penalty_weight": 0.21072105546629316,
        "trajectory_penalty_weight": 0.08959453820566239,
        "eta_optimization_factor": 1.0652943467882312,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.4598817066515166,
          0.2864868239125829,
          0.2536314694359004
        ]
      },
      "f1_score": 0.5724489795918368,
      "f2_score": 0.0,
      "f3_score": 0.25,
      "overall_score": 0.26234668280048645,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.1778984427806734,
        "w2_progressive_weight": 0.4094300685981525,
        "w3_clarification_weight": 0.41267148862117414,
        "gamma": 0.676992395099381,
        "beta": 0.522560692626419,
        "reject_threshold": 0.6605147057104646,
        "clarify_threshold": 0.5866422119418031,
        "delta1_evasion_penalty": 0.2752759928884383,
        "delta2_single_attack_penalty": 0.12457363443403997,
        "delta3_behavioral_drift_penalty": 0.08661725101397118,
        "high_escalation_threshold": 0.5834256873536192,
        "moderate_escalation_threshold": 0.5057903400235001,
        "low_credibility_threshold": 0.38070330538394215,
        "high_credibility_threshold": 0.7261619459258487,
        "credibility_penalty_weight": 0.2775502085262197,
        "trajectory_penalty_weight": 0.160946923795191,
        "eta_optimization_factor": 1.0125656519989839,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6125594863799043,
          0.16282833018396942,
          0.22461218343612624
        ]
      },
      "f1_score": 0.713265306122449,
      "f2_score": 0.0,
      "f3_score": 0.04081632653061224,
      "overall_score": 0.14373252147809967,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.28054486276211016,
        "w2_progressive_weight": 0.3808020001121129,
        "w3_clarification_weight": 0.338653137125777,
        "gamma": 0.7065076209448956,
        "beta": 0.7303501346271759,
        "reject_threshold": 0.6851842394124702,
        "clarify_threshold": 0.5368778985389699,
        "delta1_evasion_penalty": 0.2827794377712933,
        "delta2_single_attack_penalty": 0.1078204782892644,
        "delta3_behavioral_drift_penalty": 0.24589300846902973,
        "high_escalation_threshold": 0.756181179608473,
        "moderate_escalation_threshold": 0.4771988923494703,
        "low_credibility_threshold": 0.24943210006822625,
        "high_credibility_threshold": 0.7439198241513622,
        "credibility_penalty_weight": 0.254012265561009,
        "trajectory_penalty_weight": 0.22606753300211568,
        "eta_optimization_factor": 1.1424861603697924,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.47975913995201824,
          0.3911237321920545,
          0.12911712785592733
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.21156392866352616,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.19235196092544474,
        "w2_progressive_weight": 0.4844062681413111,
        "w3_clarification_weight": 0.32324177093324424,
        "gamma": 0.5680482365717998,
        "beta": 0.5761174977881722,
        "reject_threshold": 0.7118857862490439,
        "clarify_threshold": 0.31421479475319,
        "delta1_evasion_penalty": 0.32673010654494355,
        "delta2_single_attack_penalty": 0.14152682714619141,
        "delta3_behavioral_drift_penalty": 0.23143727494442534,
        "high_escalation_threshold": 0.6790982732447086,
        "moderate_escalation_threshold": 0.5982592713372397,
        "low_credibility_threshold": 0.28540726520063614,
        "high_credibility_threshold": 0.6340969335589506,
        "credibility_penalty_weight": 0.2544167711746385,
        "trajectory_penalty_weight": 0.1257530213365976,
        "eta_optimization_factor": 0.9957770967143954,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6395813930817722,
          0.11643602551539528,
          0.24398258140283258
        ]
      },
      "f1_score": 0.616326530612245,
      "f2_score": 0.0,
      "f3_score": 0.17346938775510204,
      "overall_score": 0.1746241688343063,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.24153983244412977,
        "w2_progressive_weight": 0.17706254670895033,
        "w3_clarification_weight": 0.58139762084692,
        "gamma": 0.5758939768870653,
        "beta": 0.35624660693147436,
        "reject_threshold": 0.7376822375427378,
        "clarify_threshold": 0.5899566111011502,
        "delta1_evasion_penalty": 0.200658052809492,
        "delta2_single_attack_penalty": 0.36397316617460906,
        "delta3_behavioral_drift_penalty": 0.16620073866305313,
        "high_escalation_threshold": 0.5444793099520326,
        "moderate_escalation_threshold": 0.36932813982590873,
        "low_credibility_threshold": 0.34842788797255353,
        "high_credibility_threshold": 0.5306577698289149,
        "credibility_penalty_weight": 0.22549027737116717,
        "trajectory_penalty_weight": 0.09337182181043027,
        "eta_optimization_factor": 0.9205584693445695,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.2730656092812405,
          0.4258932810096474,
          0.30104110970911213
        ]
      },
      "f1_score": 0.35714285714285715,
      "f2_score": 0.003061224489795918,
      "f3_score": 0.27040816326530615,
      "overall_score": 0.24402091685714908,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.24182788439397704,
        "w2_progressive_weight": 0.4249488744155676,
        "w3_clarification_weight": 0.3332232411904554,
        "gamma": 0.8315917036498367,
        "beta": 0.4860274541289828,
        "reject_threshold": 0.6325803336015792,
        "clarify_threshold": 0.4414255159309787,
        "delta1_evasion_penalty": 0.21536572458453018,
        "delta2_single_attack_penalty": 0.1553881870895839,
        "delta3_behavioral_drift_penalty": 0.14549506654954725,
        "high_escalation_threshold": 0.6367541158268646,
        "moderate_escalation_threshold": 0.3067825232432041,
        "low_credibility_threshold": 0.3041330598664934,
        "high_credibility_threshold": 0.6371442071234021,
        "credibility_penalty_weight": 0.13941213916926268,
        "trajectory_penalty_weight": 0.08055102688726608,
        "eta_optimization_factor": 0.8706815127256446,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.6948901327955799,
          0.20549191395984023,
          0.09961795324457988
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.18257750557830352,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.18260797698106884,
        "w2_progressive_weight": 0.37641525777135587,
        "w3_clarification_weight": 0.4409767652475753,
        "gamma": 0.5952674954610221,
        "beta": 0.7328941337825121,
        "reject_threshold": 0.8589378247649546,
        "clarify_threshold": 0.4251537427565047,
        "delta1_evasion_penalty": 0.14437195537023356,
        "delta2_single_attack_penalty": 0.41960483119076464,
        "delta3_behavioral_drift_penalty": 0.1529840486844179,
        "high_escalation_threshold": 0.7752596483380376,
        "moderate_escalation_threshold": 0.30104550283315623,
        "low_credibility_threshold": 0.25845831648971096,
        "high_credibility_threshold": 0.5883693243967857,
        "credibility_penalty_weight": 0.28990270186360323,
        "trajectory_penalty_weight": 0.16280212238883987,
        "eta_optimization_factor": 1.1173217043157309,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5077910381254114,
          0.3201494770427779,
          0.17205948483181063
        ]
      },
      "f1_score": 0.5857142857142857,
      "f2_score": 0.0,
      "f3_score": 0.21428571428571427,
      "overall_score": 0.2014511219276779,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.34545823283819455,
        "w2_progressive_weight": 0.4380572838592451,
        "w3_clarification_weight": 0.21648448330256045,
        "gamma": 0.7022476732265053,
        "beta": 0.34180906222464297,
        "reject_threshold": 0.6960806977233308,
        "clarify_threshold": 0.5512516221995301,
        "delta1_evasion_penalty": 0.375903863593974,
        "delta2_single_attack_penalty": 0.17421498261152207,
        "delta3_behavioral_drift_penalty": 0.19650740424669283,
        "high_escalation_threshold": 0.6763226421835963,
        "moderate_escalation_threshold": 0.4611750844373922,
        "low_credibility_threshold": 0.3261340895408026,
        "high_credibility_threshold": 0.535006567227076,
        "credibility_penalty_weight": 0.21548649883193358,
        "trajectory_penalty_weight": 0.15989535029637703,
        "eta_optimization_factor": 0.9126809593993447,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.5825349887061465,
          0.2585318213891146,
          0.15893318990473887
        ]
      },
      "f1_score": 0.5755102040816327,
      "f2_score": 0.0,
      "f3_score": 0.2193877551020408,
      "overall_score": 0.2463087828885635,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.07904046024175,
        "w2_progressive_weight": 0.581776629547605,
        "w3_clarification_weight": 0.33918291021064495,
        "gamma": 0.6655232803309461,
        "beta": 0.36169166476350256,
        "reject_threshold": 0.641927839910004,
        "clarify_threshold": 0.43513710162564684,
        "delta1_evasion_penalty": 0.17955512868948645,
        "delta2_single_attack_penalty": 0.21861797686751072,
        "delta3_behavioral_drift_penalty": 0.06181295443051636,
        "high_escalation_threshold": 0.5726177454951942,
        "moderate_escalation_threshold": 0.3604458996969167,
        "low_credibility_threshold": 0.3563662163186994,
        "high_credibility_threshold": 0.6418737051724832,
        "credibility_penalty_weight": 0.118820725432141,
        "trajectory_penalty_weight": 0.18235984434667235,
        "eta_optimization_factor": 1.1935137740236397,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.4947828451866464,
          0.2731216152293671,
          0.23209553958398643
        ]
      },
      "f1_score": 0.6173469387755104,
      "f2_score": 0.0,
      "f3_score": 0.17857142857142858,
      "overall_score": 0.10936376299298126,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.13262662556472174,
        "w2_progressive_weight": 0.7137909778361642,
        "w3_clarification_weight": 0.153582396599114,
        "gamma": 0.6057037130415017,
        "beta": 0.5570278731343641,
        "reject_threshold": 0.6327025936703258,
        "clarify_threshold": 0.307608838134413,
        "delta1_evasion_penalty": 0.360849584963448,
        "delta2_single_attack_penalty": 0.4487570184165137,
        "delta3_behavioral_drift_penalty": 0.23304116651673568,
        "high_escalation_threshold": 0.7621128371509913,
        "moderate_escalation_threshold": 0.5606881175159861,
        "low_credibility_threshold": 0.26700179457617884,
        "high_credibility_threshold": 0.788271200045586,
        "credibility_penalty_weight": 0.13326035555369103,
        "trajectory_penalty_weight": 0.1311481619252473,
        "eta_optimization_factor": 1.0749896630387354,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6178586829306812,
          0.26677617624557115,
          0.11536514082374764
        ]
      },
      "f1_score": 0.7285714285714285,
      "f2_score": 0.0,
      "f3_score": 0.030612244897959183,
      "overall_score": 0.10132947199100484,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3315852995621225,
        "w2_progressive_weight": 0.3425991763465763,
        "w3_clarification_weight": 0.3258155240913013,
        "gamma": 0.8149080110619956,
        "beta": 0.49091893152440863,
        "reject_threshold": 0.754945100677346,
        "clarify_threshold": 0.6254812573770494,
        "delta1_evasion_penalty": 0.34347714120161615,
        "delta2_single_attack_penalty": 0.4304541035810391,
        "delta3_behavioral_drift_penalty": 0.1713842012399061,
        "high_escalation_threshold": 0.5646433407945403,
        "moderate_escalation_threshold": 0.3485305361361282,
        "low_credibility_threshold": 0.33871117248839594,
        "high_credibility_threshold": 0.6101004220922754,
        "credibility_penalty_weight": 0.21896430936053213,
        "trajectory_penalty_weight": 0.11899879668068969,
        "eta_optimization_factor": 0.9529534677846475,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.5824155615369158,
          0.31961512635782013,
          0.09796931210526412
        ]
      },
      "f1_score": 0.6673469387755102,
      "f2_score": 0.0,
      "f3_score": 0.10714285714285714,
      "overall_score": 0.2561912407583824,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.257480606499043,
        "w2_progressive_weight": 0.5425412215290086,
        "w3_clarification_weight": 0.1999781719719482,
        "gamma": 0.6798352839388151,
        "beta": 0.7840468138145588,
        "reject_threshold": 0.6631950745670431,
        "clarify_threshold": 0.5906054540183231,
        "delta1_evasion_penalty": 0.29929635167458524,
        "delta2_single_attack_penalty": 0.1337847983924736,
        "delta3_behavioral_drift_penalty": 0.2472472604362147,
        "high_escalation_threshold": 0.7639768697826639,
        "moderate_escalation_threshold": 0.36266766827394376,
        "low_credibility_threshold": 0.2485761263516269,
        "high_credibility_threshold": 0.7992187021184978,
        "credibility_penalty_weight": 0.2393348405811986,
        "trajectory_penalty_weight": 0.176889441585231,
        "eta_optimization_factor": 1.0062405622795945,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.18128777407598892,
          0.509303394518602,
          0.30940883140540915
        ]
      },
      "f1_score": 0.7448979591836735,
      "f2_score": 0.0,
      "f3_score": 0.0,
      "overall_score": 0.19179677831051162,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.28401635758338384,
        "w2_progressive_weight": 0.1239655345136911,
        "w3_clarification_weight": 0.5920181079029251,
        "gamma": 0.5007660815295579,
        "beta": 0.46071374419115985,
        "reject_threshold": 0.8531370833272773,
        "clarify_threshold": 0.6754094394281653,
        "delta1_evasion_penalty": 0.27472387244547924,
        "delta2_single_attack_penalty": 0.19993524949917973,
        "delta3_behavioral_drift_penalty": 0.1476339580930081,
        "high_escalation_threshold": 0.6954447741309946,
        "moderate_escalation_threshold": 0.547506642256951,
        "low_credibility_threshold": 0.20501510588649907,
        "high_credibility_threshold": 0.7512353271096758,
        "credibility_penalty_weight": 0.27102121333961593,
        "trajectory_penalty_weight": 0.1073049086060499,
        "eta_optimization_factor": 0.8302536870848061,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.2267002099481846,
          0.41946208724789225,
          0.35383770280392324
        ]
      },
      "f1_score": 0.3153061224489796,
      "f2_score": 0.018367346938775512,
      "f3_score": 0.2653061224489796,
      "overall_score": 0.24889504302986998,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.2852107800257544,
        "w2_progressive_weight": 0.5224455932777563,
        "w3_clarification_weight": 0.19234362669648933,
        "gamma": 0.716171486017007,
        "beta": 0.4718496532781255,
        "reject_threshold": 0.696072290793394,
        "clarify_threshold": 0.42642119156135827,
        "delta1_evasion_penalty": 0.2422024710514519,
        "delta2_single_attack_penalty": 0.3549020936570889,
        "delta3_behavioral_drift_penalty": 0.18112021608501722,
        "high_escalation_threshold": 0.766599956609249,
        "moderate_escalation_threshold": 0.5220183727561363,
        "low_credibility_threshold": 0.3539988486414041,
        "high_credibility_threshold": 0.662188100503377,
        "credibility_penalty_weight": 0.2954165555945648,
        "trajectory_penalty_weight": 0.0891402212738375,
        "eta_optimization_factor": 0.9914513369170925,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.46278812776688766,
          0.3236354858995068,
          0.21357638633360562
        ]
      },
      "f1_score": 0.6632653061224489,
      "f2_score": 0.0,
      "f3_score": 0.11224489795918366,
      "overall_score": 0.2107600060748512,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.22012982009682155,
        "w2_progressive_weight": 0.625793300560113,
        "w3_clarification_weight": 0.15407687934306538,
        "gamma": 0.5762612067138039,
        "beta": 0.5588031608521011,
        "reject_threshold": 0.7877299351928259,
        "clarify_threshold": 0.6837192265786467,
        "delta1_evasion_penalty": 0.25643557899064584,
        "delta2_single_attack_penalty": 0.3472367952774612,
        "delta3_behavioral_drift_penalty": 0.06255955545513107,
        "high_escalation_threshold": 0.5654792769174429,
        "moderate_escalation_threshold": 0.5596166578672804,
        "low_credibility_threshold": 0.34581444720995413,
        "high_credibility_threshold": 0.755090018645155,
        "credibility_penalty_weight": 0.2746262692885485,
        "trajectory_penalty_weight": 0.243625383181927,
        "eta_optimization_factor": 0.9296527998197311,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.551764703664302,
          0.22333075905809732,
          0.2249045372776008
        ]
      },
      "f1_score": 0.5051020408163265,
      "f2_score": 0.0,
      "f3_score": 0.15306122448979592,
      "overall_score": 0.1347712171932515,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3121626327880142,
        "w2_progressive_weight": 0.48488127472007675,
        "w3_clarification_weight": 0.2029560924919091,
        "gamma": 0.7535716364106096,
        "beta": 0.37612357802767854,
        "reject_threshold": 0.7565864531360851,
        "clarify_threshold": 0.6565864531360851,
        "delta1_evasion_penalty": 0.3199370399039594,
        "delta2_single_attack_penalty": 0.2471996505728268,
        "delta3_behavioral_drift_penalty": 0.26641429305064873,
        "high_escalation_threshold": 0.5978755288083079,
        "moderate_escalation_threshold": 0.5759823398397472,
        "low_credibility_threshold": 0.35045418499267744,
        "high_credibility_threshold": 0.6113906626253718,
        "credibility_penalty_weight": 0.2666387605212548,
        "trajectory_penalty_weight": 0.2150682099883982,
        "eta_optimization_factor": 1.0312464114912294,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6269487215811281,
          0.2865140701821384,
          0.0865372082367335
        ]
      },
      "f1_score": 0.5510204081632654,
      "f2_score": 0.0,
      "f3_score": 0.14285714285714285,
      "overall_score": 0.20100170883101526,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.4206787905789847,
        "w2_progressive_weight": 0.27915252761166953,
        "w3_clarification_weight": 0.30016868180934586,
        "gamma": 0.7419701971730653,
        "beta": 0.32797824517977964,
        "reject_threshold": 0.6962639925954476,
        "clarify_threshold": 0.6807057318974147,
        "delta1_evasion_penalty": 0.3185279638763484,
        "delta2_single_attack_penalty": 0.21736350332240192,
        "delta3_behavioral_drift_penalty": 0.18100642917785342,
        "high_escalation_threshold": 0.678847367727017,
        "moderate_escalation_threshold": 0.572677863845467,
        "low_credibility_threshold": 0.3366606442405603,
        "high_credibility_threshold": 0.6813905632050672,
        "credibility_penalty_weight": 0.19261513916612943,
        "trajectory_penalty_weight": 0.18080560027015685,
        "eta_optimization_factor": 1.1714700458736584,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5104113015484042,
          0.4089321199689873,
          0.08065657848260854
        ]
      },
      "f1_score": 0.5581632653061224,
      "f2_score": 0.0,
      "f3_score": 0.02040816326530612,
      "overall_score": 0.2409333388600934,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3224911177472835,
        "w2_progressive_weight": 0.46374690952172,
        "w3_clarification_weight": 0.21376197273099642,
        "gamma": 0.8964685396086789,
        "beta": 0.4318610388224067,
        "reject_threshold": 0.7599765150895452,
        "clarify_threshold": 0.449616830961833,
        "delta1_evasion_penalty": 0.35783323107875065,
        "delta2_single_attack_penalty": 0.17722132429673365,
        "delta3_behavioral_drift_penalty": 0.10896892026034165,
        "high_escalation_threshold": 0.7433794850915199,
        "moderate_escalation_threshold": 0.5656896599027659,
        "low_credibility_threshold": 0.21483061928862238,
        "high_credibility_threshold": 0.5353956941098826,
        "credibility_penalty_weight": 0.26112801166420296,
        "trajectory_penalty_weight": 0.1380094943355348,
        "eta_optimization_factor": 0.8012853141295009,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.6822653217189768,
          0.1429999380186492,
          0.17473474026237396
        ]
      },
      "f1_score": 0.6908163265306123,
      "f2_score": 0.0,
      "f3_score": 0.08163265306122448,
      "overall_score": 0.2402320862585619,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.2843540472207981,
        "w2_progressive_weight": 0.5763614232447142,
        "w3_clarification_weight": 0.13928452953448767,
        "gamma": 0.8836806593887162,
        "beta": 0.41616871824566815,
        "reject_threshold": 0.6488138054754383,
        "clarify_threshold": 0.3546866811782261,
        "delta1_evasion_penalty": 0.24660336810282002,
        "delta2_single_attack_penalty": 0.35720272439526146,
        "delta3_behavioral_drift_penalty": 0.11726794739128384,
        "high_escalation_threshold": 0.6151992689276284,
        "moderate_escalation_threshold": 0.5829026172660009,
        "low_credibility_threshold": 0.27186390460944454,
        "high_credibility_threshold": 0.5768141481281477,
        "credibility_penalty_weight": 0.17020935736142667,
        "trajectory_penalty_weight": 0.13977807151577987,
        "eta_optimization_factor": 0.9867488722627954,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5850221830148592,
          0.33620893078713115,
          0.07876888619800959
        ]
      },
      "f1_score": 0.7438775510204083,
      "f2_score": 0.0,
      "f3_score": 0.01020408163265306,
      "overall_score": 0.21294586297888443,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3561194800569364,
        "w2_progressive_weight": 0.4153067240230739,
        "w3_clarification_weight": 0.22857379591998986,
        "gamma": 0.7653739952168869,
        "beta": 0.30295956024193066,
        "reject_threshold": 0.8658547930898959,
        "clarify_threshold": 0.6051590824433815,
        "delta1_evasion_penalty": 0.20673107519225806,
        "delta2_single_attack_penalty": 0.12442289236261828,
        "delta3_behavioral_drift_penalty": 0.12335203950139663,
        "high_escalation_threshold": 0.6464125943592758,
        "moderate_escalation_threshold": 0.4018945831325522,
        "low_credibility_threshold": 0.3860925026776056,
        "high_credibility_threshold": 0.6056593277589354,
        "credibility_penalty_weight": 0.12244604698392632,
        "trajectory_penalty_weight": 0.21362233357539528,
        "eta_optimization_factor": 0.8511191307370702,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.45343637431741146,
          0.31658140716419036,
          0.22998221851839817
        ]
      },
      "f1_score": 0.38469387755102036,
      "f2_score": 0.001020408163265306,
      "f3_score": 0.42857142857142855,
      "overall_score": 0.23538096427743244,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3905645386813277,
        "w2_progressive_weight": 0.34465679813958827,
        "w3_clarification_weight": 0.2647786631790842,
        "gamma": 0.6998401384869558,
        "beta": 0.465359148427501,
        "reject_threshold": 0.6924607094559229,
        "clarify_threshold": 0.5237572974371729,
        "delta1_evasion_penalty": 0.1301054622642161,
        "delta2_single_attack_penalty": 0.4219255353843927,
        "delta3_behavioral_drift_penalty": 0.278912414226896,
        "high_escalation_threshold": 0.5567300617276968,
        "moderate_escalation_threshold": 0.40401282604153593,
        "low_credibility_threshold": 0.23990955507484546,
        "high_credibility_threshold": 0.5145665564655463,
        "credibility_penalty_weight": 0.26670280539218844,
        "trajectory_penalty_weight": 0.20995198820580552,
        "eta_optimization_factor": 1.0340096773999055,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.7918508254725883,
          0.13161582364078453,
          0.0765333508866272
        ]
      },
      "f1_score": 0.6612244897959183,
      "f2_score": 0.0,
      "f3_score": 0.11224489795918366,
      "overall_score": 0.28797089185224445,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.22763551151117353,
        "w2_progressive_weight": 0.3347437797585002,
        "w3_clarification_weight": 0.43762070873032616,
        "gamma": 0.6253067919456577,
        "beta": 0.7700507035955371,
        "reject_threshold": 0.6454986909072241,
        "clarify_threshold": 0.45597341557916227,
        "delta1_evasion_penalty": 0.16966807667203376,
        "delta2_single_attack_penalty": 0.3697424840640591,
        "delta3_behavioral_drift_penalty": 0.0904595114095544,
        "high_escalation_threshold": 0.5234123851281212,
        "moderate_escalation_threshold": 0.5049873781051152,
        "low_credibility_threshold": 0.2754783239649472,
        "high_credibility_threshold": 0.5126746392476415,
        "credibility_penalty_weight": 0.1636049242908479,
        "trajectory_penalty_weight": 0.05225618779809722,
        "eta_optimization_factor": 1.161789060091585,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.5027040462780165,
          0.2223963772058015,
          0.274899576516182
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.17249483008300187,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.22722994678749228,
        "w2_progressive_weight": 0.2581403608241969,
        "w3_clarification_weight": 0.5146296923883108,
        "gamma": 0.8840446415244394,
        "beta": 0.43794341680404214,
        "reject_threshold": 0.6694897762230568,
        "clarify_threshold": 0.6040235559627185,
        "delta1_evasion_penalty": 0.33828163075476747,
        "delta2_single_attack_penalty": 0.23061118401933572,
        "delta3_behavioral_drift_penalty": 0.26497569770729745,
        "high_escalation_threshold": 0.6776481972115157,
        "moderate_escalation_threshold": 0.5813963866949559,
        "low_credibility_threshold": 0.21373157118946928,
        "high_credibility_threshold": 0.7658750941444237,
        "credibility_penalty_weight": 0.26440550251312356,
        "trajectory_penalty_weight": 0.2090962012481528,
        "eta_optimization_factor": 1.0902075716550288,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5708266453253006,
          0.29447980697171466,
          0.13469354770298472
        ]
      },
      "f1_score": 0.7448979591836735,
      "f2_score": 0.0,
      "f3_score": 0.0,
      "overall_score": 0.16926312362741774,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.18527619407899307,
        "w2_progressive_weight": 0.36685486158204006,
        "w3_clarification_weight": 0.4478689443389668,
        "gamma": 0.7742344586011869,
        "beta": 0.33953113631291204,
        "reject_threshold": 0.8023850411649173,
        "clarify_threshold": 0.4125298604045738,
        "delta1_evasion_penalty": 0.3299109719774739,
        "delta2_single_attack_penalty": 0.17815357356099581,
        "delta3_behavioral_drift_penalty": 0.05237541276618703,
        "high_escalation_threshold": 0.7940237741746086,
        "moderate_escalation_threshold": 0.3229186705251019,
        "low_credibility_threshold": 0.3438376606520325,
        "high_credibility_threshold": 0.6754269946405931,
        "credibility_penalty_weight": 0.18817034177892164,
        "trajectory_penalty_weight": 0.10403427569860985,
        "eta_optimization_factor": 0.8268043389577908,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.46839667045016936,
          0.29941395378644825,
          0.23218937576338236
        ]
      },
      "f1_score": 0.49183673469387756,
      "f2_score": 0.0,
      "f3_score": 0.3622448979591837,
      "overall_score": 0.2533638783534774,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.19836392425692373,
        "w2_progressive_weight": 0.2692837689804907,
        "w3_clarification_weight": 0.5323523067625855,
        "gamma": 0.735273789329797,
        "beta": 0.3886489979149657,
        "reject_threshold": 0.7860605138344751,
        "clarify_threshold": 0.6726669032804335,
        "delta1_evasion_penalty": 0.22959492490554345,
        "delta2_single_attack_penalty": 0.3958931768538261,
        "delta3_behavioral_drift_penalty": 0.10834983669723426,
        "high_escalation_threshold": 0.5923496549832735,
        "moderate_escalation_threshold": 0.5371611175071491,
        "low_credibility_threshold": 0.3106490504947598,
        "high_credibility_threshold": 0.6986971526033874,
        "credibility_penalty_weight": 0.16191578108912819,
        "trajectory_penalty_weight": 0.12731738524936953,
        "eta_optimization_factor": 0.9705373348011791,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.5979521748814765,
          0.26396257409111756,
          0.138085251027406
        ]
      },
      "f1_score": 0.45816326530612245,
      "f2_score": 0.0,
      "f3_score": 0.20408163265306123,
      "overall_score": 0.19952639116722026,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.2390257621207966,
        "w2_progressive_weight": 0.5244974712533246,
        "w3_clarification_weight": 0.2364767666258789,
        "gamma": 0.5057885929210657,
        "beta": 0.6856216566749423,
        "reject_threshold": 0.7192967197968531,
        "clarify_threshold": 0.4381704877751317,
        "delta1_evasion_penalty": 0.38010130219972205,
        "delta2_single_attack_penalty": 0.26696643506402223,
        "delta3_behavioral_drift_penalty": 0.07863676640778218,
        "high_escalation_threshold": 0.6717870807946369,
        "moderate_escalation_threshold": 0.35400082221106877,
        "low_credibility_threshold": 0.24995184982202717,
        "high_credibility_threshold": 0.6361589541538222,
        "credibility_penalty_weight": 0.21304445176408932,
        "trajectory_penalty_weight": 0.11880298543594754,
        "eta_optimization_factor": 0.9990801283393497,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6993379357758043,
          0.12474010645316505,
          0.17592195777103065
        ]
      },
      "f1_score": 0.6316326530612245,
      "f2_score": 0.0,
      "f3_score": 0.15816326530612246,
      "overall_score": 0.1883784138569228,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.1292081619594894,
        "w2_progressive_weight": 0.3851336321976452,
        "w3_clarification_weight": 0.4856582058428653,
        "gamma": 0.780081742829162,
        "beta": 0.4192111384318221,
        "reject_threshold": 0.6925516329533967,
        "clarify_threshold": 0.3368667284601086,
        "delta1_evasion_penalty": 0.1870288641885242,
        "delta2_single_attack_penalty": 0.49564053987585865,
        "delta3_behavioral_drift_penalty": 0.27136540096617995,
        "high_escalation_threshold": 0.6784688133136314,
        "moderate_escalation_threshold": 0.36300164216276165,
        "low_credibility_threshold": 0.36621053133706616,
        "high_credibility_threshold": 0.7420453941480267,
        "credibility_penalty_weight": 0.13947337955189887,
        "trajectory_penalty_weight": 0.051280115340977254,
        "eta_optimization_factor": 1.1147947683244464,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5744029458774745,
          0.2115734568086042,
          0.21402359731392132
        ]
      },
      "f1_score": 0.6704081632653062,
      "f2_score": 0.0,
      "f3_score": 0.11224489795918366,
      "overall_score": 0.14113486229602012,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.17686180119095735,
        "w2_progressive_weight": 0.3797346656868793,
        "w3_clarification_weight": 0.4434035331221633,
        "gamma": 0.6957358929259262,
        "beta": 0.6480240484421222,
        "reject_threshold": 0.7526469149072551,
        "clarify_threshold": 0.40577652507235923,
        "delta1_evasion_penalty": 0.308058599879399,
        "delta2_single_attack_penalty": 0.37023097516761805,
        "delta3_behavioral_drift_penalty": 0.11298342938180518,
        "high_escalation_threshold": 0.5148760629608684,
        "moderate_escalation_threshold": 0.4097614018446599,
        "low_credibility_threshold": 0.3857579635318972,
        "high_credibility_threshold": 0.6860278510901657,
        "credibility_penalty_weight": 0.22640790883150624,
        "trajectory_penalty_weight": 0.19239211389316985,
        "eta_optimization_factor": 1.005628356086014,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6334910413811993,
          0.09926086860929091,
          0.2672480900095098
        ]
      },
      "f1_score": 0.7051020408163265,
      "f2_score": 0.0,
      "f3_score": 0.061224489795918366,
      "overall_score": 0.15185277205130748,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.4208812856664296,
        "w2_progressive_weight": 0.1521484906178638,
        "w3_clarification_weight": 0.4269702237157066,
        "gamma": 0.6219518457157767,
        "beta": 0.5104464863414402,
        "reject_threshold": 0.6279415267449592,
        "clarify_threshold": 0.5072396267337048,
        "delta1_evasion_penalty": 0.3549110242147744,
        "delta2_single_attack_penalty": 0.15651410352332645,
        "delta3_behavioral_drift_penalty": 0.2460712597198766,
        "high_escalation_threshold": 0.642029712255778,
        "moderate_escalation_threshold": 0.4933610285011859,
        "low_credibility_threshold": 0.20142416842640345,
        "high_credibility_threshold": 0.5042037737758276,
        "credibility_penalty_weight": 0.13795840647140453,
        "trajectory_penalty_weight": 0.0707451049150313,
        "eta_optimization_factor": 0.9474212868140522,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6528624053587293,
          0.21029133646471437,
          0.1368462581765563
        ]
      },
      "f1_score": 0.7081632653061225,
      "f2_score": 0.0,
      "f3_score": 0.05102040816326531,
      "overall_score": 0.3198368606513138,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.24400202332313992,
        "w2_progressive_weight": 0.47993470492162943,
        "w3_clarification_weight": 0.27606327175523065,
        "gamma": 0.7113425613846727,
        "beta": 0.42887264730669,
        "reject_threshold": 0.6568979688562105,
        "clarify_threshold": 0.31364424656443185,
        "delta1_evasion_penalty": 0.21676608181880505,
        "delta2_single_attack_penalty": 0.2904210327520894,
        "delta3_behavioral_drift_penalty": 0.2280486744955671,
        "high_escalation_threshold": 0.7004702506209534,
        "moderate_escalation_threshold": 0.35964087794504734,
        "low_credibility_threshold": 0.2750294539909555,
        "high_credibility_threshold": 0.6703836609677369,
        "credibility_penalty_weight": 0.16961700154066578,
        "trajectory_penalty_weight": 0.22335507487370515,
        "eta_optimization_factor": 0.9356995747145941,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.48862082156225056,
          0.31059332305582743,
          0.2007858553819221
        ]
      },
      "f1_score": 0.6928571428571428,
      "f2_score": 0.0,
      "f3_score": 0.08163265306122448,
      "overall_score": 0.19159432201717394,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.5101136553239267,
        "w2_progressive_weight": 0.23767738652886566,
        "w3_clarification_weight": 0.2522089581472076,
        "gamma": 0.5849175217542747,
        "beta": 0.30646976644759427,
        "reject_threshold": 0.8470759189120258,
        "clarify_threshold": 0.31430553074263484,
        "delta1_evasion_penalty": 0.2540857978844038,
        "delta2_single_attack_penalty": 0.26921334171865935,
        "delta3_behavioral_drift_penalty": 0.05924135301007398,
        "high_escalation_threshold": 0.6982323876796903,
        "moderate_escalation_threshold": 0.38324756760450596,
        "low_credibility_threshold": 0.23719425051378618,
        "high_credibility_threshold": 0.5302098447606438,
        "credibility_penalty_weight": 0.1582293397269749,
        "trajectory_penalty_weight": 0.22524819688961395,
        "eta_optimization_factor": 0.9109763151283445,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.7300133048029583,
          0.1397321009148838,
          0.13025459428215785
        ]
      },
      "f1_score": 0.3734693877551021,
      "f2_score": 0.038775510204081626,
      "f3_score": 0.6428571428571429,
      "overall_score": 0.36186222670346424,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.2917726209302231,
        "w2_progressive_weight": 0.35262601342080824,
        "w3_clarification_weight": 0.35560136564896855,
        "gamma": 0.5190665718761053,
        "beta": 0.78696090863476,
        "reject_threshold": 0.8866775991830732,
        "clarify_threshold": 0.5966677220487564,
        "delta1_evasion_penalty": 0.22000072051132696,
        "delta2_single_attack_penalty": 0.4424204349498869,
        "delta3_behavioral_drift_penalty": 0.0524356602385808,
        "high_escalation_threshold": 0.7813696211396739,
        "moderate_escalation_threshold": 0.3295628804777486,
        "low_credibility_threshold": 0.3568879776009292,
        "high_credibility_threshold": 0.5613205325390056,
        "credibility_penalty_weight": 0.2004368192068054,
        "trajectory_penalty_weight": 0.18410230028559832,
        "eta_optimization_factor": 0.9981191642086358,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5896364944270596,
          0.2403609925441317,
          0.17000251302880878
        ]
      },
      "f1_score": 0.5653061224489796,
      "f2_score": 0.003061224489795918,
      "f3_score": 0.25510204081632654,
      "overall_score": 0.2567349504569879,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.4591576191946688,
        "w2_progressive_weight": 0.17936771343018393,
        "w3_clarification_weight": 0.3614746673751473,
        "gamma": 0.8399233997017865,
        "beta": 0.7314591237516417,
        "reject_threshold": 0.814768606317489,
        "clarify_threshold": 0.4865206229605281,
        "delta1_evasion_penalty": 0.33749389491497794,
        "delta2_single_attack_penalty": 0.1640205114169361,
        "delta3_behavioral_drift_penalty": 0.061059364849602546,
        "high_escalation_threshold": 0.6307904570086386,
        "moderate_escalation_threshold": 0.5541243517506855,
        "low_credibility_threshold": 0.31075948273082754,
        "high_credibility_threshold": 0.7901031575772693,
        "credibility_penalty_weight": 0.16692257390123943,
        "trajectory_penalty_weight": 0.053705955903040836,
        "eta_optimization_factor": 0.9658806725714363,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.5381820372914928,
          0.3367257336713147,
          0.12509222903719242
        ]
      },
      "f1_score": 0.7408163265306124,
      "f2_score": 0.0,
      "f3_score": 0.015306122448979591,
      "overall_score": 0.3456842362713845,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.5062201675353123,
        "w2_progressive_weight": 0.0991651696221716,
        "w3_clarification_weight": 0.3946146628425161,
        "gamma": 0.691096101406613,
        "beta": 0.43577609076806323,
        "reject_threshold": 0.6999189796629389,
        "clarify_threshold": 0.3064596605773547,
        "delta1_evasion_penalty": 0.33261444494533926,
        "delta2_single_attack_penalty": 0.44627244837526814,
        "delta3_behavioral_drift_penalty": 0.2248869077284671,
        "high_escalation_threshold": 0.652186966830047,
        "moderate_escalation_threshold": 0.4723227724333737,
        "low_credibility_threshold": 0.2035437220674311,
        "high_credibility_threshold": 0.6283556760139036,
        "credibility_penalty_weight": 0.1415054694345335,
        "trajectory_penalty_weight": 0.2077206964992177,
        "eta_optimization_factor": 1.1569453504299543,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6600961134215292,
          0.19201973203538225,
          0.14788415454308862
        ]
      },
      "f1_score": 0.6234693877551021,
      "f2_score": 0.0,
      "f3_score": 0.1683673469387755,
      "overall_score": 0.3820530017684602,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.35718948004588674,
        "w2_progressive_weight": 0.22524602349106537,
        "w3_clarification_weight": 0.4175644964630479,
        "gamma": 0.7877266715468078,
        "beta": 0.6576404531116457,
        "reject_threshold": 0.6118908254853639,
        "clarify_threshold": 0.560488546878142,
        "delta1_evasion_penalty": 0.23677687672330497,
        "delta2_single_attack_penalty": 0.4544708017713879,
        "delta3_behavioral_drift_penalty": 0.081368066575204,
        "high_escalation_threshold": 0.7643455101940895,
        "moderate_escalation_threshold": 0.5131219844922636,
        "low_credibility_threshold": 0.3064207411784421,
        "high_credibility_threshold": 0.7476545427896699,
        "credibility_penalty_weight": 0.24812954912291615,
        "trajectory_penalty_weight": 0.2210257890460649,
        "eta_optimization_factor": 0.884757547470746,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.6221933680506349,
          0.16417616747995065,
          0.2136304644694144
        ]
      },
      "f1_score": 0.75,
      "f2_score": 0.0,
      "f3_score": 0.0,
      "overall_score": 0.26789211003441504,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.5748933096553557,
        "w2_progressive_weight": 0.19619019645620472,
        "w3_clarification_weight": 0.22891649388843957,
        "gamma": 0.8575932591024593,
        "beta": 0.6850256688253468,
        "reject_threshold": 0.7904294884405076,
        "clarify_threshold": 0.42519928035803845,
        "delta1_evasion_penalty": 0.1979063082077031,
        "delta2_single_attack_penalty": 0.4848282579839691,
        "delta3_behavioral_drift_penalty": 0.2811510001703191,
        "high_escalation_threshold": 0.6920579306996113,
        "moderate_escalation_threshold": 0.4039551779779312,
        "low_credibility_threshold": 0.300955445170456,
        "high_credibility_threshold": 0.7020540022408099,
        "credibility_penalty_weight": 0.10585281072208985,
        "trajectory_penalty_weight": 0.16319586433957117,
        "eta_optimization_factor": 1.0051701733399805,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.7513705275671495,
          0.13501536969656774,
          0.11361410273628288
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.4311646718845081,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.24751882276593334,
        "w2_progressive_weight": 0.1092374419398472,
        "w3_clarification_weight": 0.6432437352942194,
        "gamma": 0.8669884825586534,
        "beta": 0.6132570113600919,
        "reject_threshold": 0.6294264494252316,
        "clarify_threshold": 0.5605351554872222,
        "delta1_evasion_penalty": 0.15188612081084996,
        "delta2_single_attack_penalty": 0.29913499330197935,
        "delta3_behavioral_drift_penalty": 0.1957135928457006,
        "high_escalation_threshold": 0.7684094456610269,
        "moderate_escalation_threshold": 0.5913822121716878,
        "low_credibility_threshold": 0.2694506446336835,
        "high_credibility_threshold": 0.6878601581444997,
        "credibility_penalty_weight": 0.13258155113736106,
        "trajectory_penalty_weight": 0.16220371956998747,
        "eta_optimization_factor": 1.0679894486416066,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.5152224881047025,
          0.23681498171810833,
          0.24796253017718922
        ]
      },
      "f1_score": 0.75,
      "f2_score": 0.0,
      "f3_score": 0.0,
      "overall_score": 0.18563911707445002,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.6679669499793587,
        "w2_progressive_weight": 0.22678209030772858,
        "w3_clarification_weight": 0.10525095971291289,
        "gamma": 0.817215478595161,
        "beta": 0.7584464922500815,
        "reject_threshold": 0.7620311442937231,
        "clarify_threshold": 0.44248019948395645,
        "delta1_evasion_penalty": 0.12081267600095658,
        "delta2_single_attack_penalty": 0.10537265116975952,
        "delta3_behavioral_drift_penalty": 0.09041202684866749,
        "high_escalation_threshold": 0.6139326593536202,
        "moderate_escalation_threshold": 0.5384816076449921,
        "low_credibility_threshold": 0.25866878215421385,
        "high_credibility_threshold": 0.5262641789460258,
        "credibility_penalty_weight": 0.22713712748117224,
        "trajectory_penalty_weight": 0.21806993137153313,
        "eta_optimization_factor": 1.1646041277289167,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.2820483676381735,
          0.5191833690050707,
          0.19876826335675588
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.5001490093198311,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.21877596764710394,
        "w2_progressive_weight": 0.6530586292995038,
        "w3_clarification_weight": 0.12816540305339238,
        "gamma": 0.5404006118867173,
        "beta": 0.6931701075783822,
        "reject_threshold": 0.6017095682026624,
        "clarify_threshold": 0.5288955391901944,
        "delta1_evasion_penalty": 0.37457949665082557,
        "delta2_single_attack_penalty": 0.30919963180833,
        "delta3_behavioral_drift_penalty": 0.1613111861236986,
        "high_escalation_threshold": 0.7181412916913128,
        "moderate_escalation_threshold": 0.5986754848653163,
        "low_credibility_threshold": 0.3006154978030978,
        "high_credibility_threshold": 0.7398758834150897,
        "credibility_penalty_weight": 0.2759520312466084,
        "trajectory_penalty_weight": 0.24507259479251764,
        "eta_optimization_factor": 0.986205797107406,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.3882048047418779,
          0.5023490499179551,
          0.10944614534016686
        ]
      },
      "f1_score": 0.7448979591836735,
      "f2_score": 0.0,
      "f3_score": 0.0,
      "overall_score": 0.16296577181876112,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.14391825660548435,
        "w2_progressive_weight": 0.3924712707722125,
        "w3_clarification_weight": 0.46361047262230315,
        "gamma": 0.9379794453716552,
        "beta": 0.42395712496768845,
        "reject_threshold": 0.8844562533275284,
        "clarify_threshold": 0.30351256786340336,
        "delta1_evasion_penalty": 0.2017842841378577,
        "delta2_single_attack_penalty": 0.4997398527904806,
        "delta3_behavioral_drift_penalty": 0.13265048570548937,
        "high_escalation_threshold": 0.6319437131917989,
        "moderate_escalation_threshold": 0.3313337021555716,
        "low_credibility_threshold": 0.3574163480946867,
        "high_credibility_threshold": 0.5122047557304324,
        "credibility_penalty_weight": 0.1357089967453074,
        "trajectory_penalty_weight": 0.18044753339515052,
        "eta_optimization_factor": 1.0018427839561554,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.557129588634123,
          0.23910922771312,
          0.20376118365275703
        ]
      },
      "f1_score": 0.5908163265306122,
      "f2_score": 0.0,
      "f3_score": 0.2295918367346939,
      "overall_score": 0.1914704356271364,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.30121403636230626,
        "w2_progressive_weight": 0.14604719000639377,
        "w3_clarification_weight": 0.5527387736313,
        "gamma": 0.5472881519106385,
        "beta": 0.4571169329931474,
        "reject_threshold": 0.7768598248145213,
        "clarify_threshold": 0.49193818689361485,
        "delta1_evasion_penalty": 0.3614543721337502,
        "delta2_single_attack_penalty": 0.4019619338703482,
        "delta3_behavioral_drift_penalty": 0.1605642078388268,
        "high_escalation_threshold": 0.5849711541541543,
        "moderate_escalation_threshold": 0.30802057695406937,
        "low_credibility_threshold": 0.2688038148167038,
        "high_credibility_threshold": 0.75252999347663,
        "credibility_penalty_weight": 0.20019615807852592,
        "trajectory_penalty_weight": 0.07071512978235056,
        "eta_optimization_factor": 1.0104820263478274,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.7128848172976575,
          0.09529715094272757,
          0.19181803175961493
        ]
      },
      "f1_score": 0.4591836734693878,
      "f2_score": 0.00510204081632653,
      "f3_score": 0.423469387755102,
      "overall_score": 0.37312565650006047,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3666190907201761,
        "w2_progressive_weight": 0.23323459871146882,
        "w3_clarification_weight": 0.40014631056835515,
        "gamma": 0.8633245253756558,
        "beta": 0.5915724877212818,
        "reject_threshold": 0.7269200874746957,
        "clarify_threshold": 0.46521110287756606,
        "delta1_evasion_penalty": 0.31104190575208657,
        "delta2_single_attack_penalty": 0.10740532479074641,
        "delta3_behavioral_drift_penalty": 0.2857789825203581,
        "high_escalation_threshold": 0.7604897630417362,
        "moderate_escalation_threshold": 0.5809707507940167,
        "low_credibility_threshold": 0.3129861690439033,
        "high_credibility_threshold": 0.6100196789510266,
        "credibility_penalty_weight": 0.191215341233408,
        "trajectory_penalty_weight": 0.10803675644478505,
        "eta_optimization_factor": 1.0483306643989099,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.3342712122250818,
          0.4135427580501247,
          0.2521860297247935
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.2762576786231948,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.30150794129379416,
        "w2_progressive_weight": 0.19351405324533347,
        "w3_clarification_weight": 0.5049780054608725,
        "gamma": 0.712334371182754,
        "beta": 0.7221539122297249,
        "reject_threshold": 0.7292561605772327,
        "clarify_threshold": 0.5300166390721613,
        "delta1_evasion_penalty": 0.18816547734328237,
        "delta2_single_attack_penalty": 0.40065845853197624,
        "delta3_behavioral_drift_penalty": 0.10713092062585243,
        "high_escalation_threshold": 0.5401551501495766,
        "moderate_escalation_threshold": 0.43383913643960104,
        "low_credibility_threshold": 0.2534578141064126,
        "high_credibility_threshold": 0.7225431727866539,
        "credibility_penalty_weight": 0.11363591084411694,
        "trajectory_penalty_weight": 0.10711023099356674,
        "eta_optimization_factor": 1.0064171291734811,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.7237972754742908,
          0.1363423696583475,
          0.13986035486736173
        ]
      },
      "f1_score": 0.7408163265306124,
      "f2_score": 0.0,
      "f3_score": 0.015306122448979591,
      "overall_score": 0.2310912606747017,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.5808685764885534,
        "w2_progressive_weight": 0.2674942063108278,
        "w3_clarification_weight": 0.15163721720061868,
        "gamma": 0.7783245624782864,
        "beta": 0.5567292279334599,
        "reject_threshold": 0.6918101180810722,
        "clarify_threshold": 0.44916337449726906,
        "delta1_evasion_penalty": 0.29494484547601657,
        "delta2_single_attack_penalty": 0.286169894225494,
        "delta3_behavioral_drift_penalty": 0.14291412539905218,
        "high_escalation_threshold": 0.7894649489090986,
        "moderate_escalation_threshold": 0.40791834258289106,
        "low_credibility_threshold": 0.2013734875019766,
        "high_credibility_threshold": 0.760093227951098,
        "credibility_penalty_weight": 0.18044815867862102,
        "trajectory_penalty_weight": 0.23318704888492098,
        "eta_optimization_factor": 1.1480508178937154,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.5727931580629148,
          0.33093018987966505,
          0.0962766520574202
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.43523964556338035,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.5522309355980088,
        "w2_progressive_weight": 0.19460435706291812,
        "w3_clarification_weight": 0.253164707339073,
        "gamma": 0.7312169298771154,
        "beta": 0.593041442575043,
        "reject_threshold": 0.7107042207605969,
        "clarify_threshold": 0.44104732060013735,
        "delta1_evasion_penalty": 0.11037497471946629,
        "delta2_single_attack_penalty": 0.19946133580088465,
        "delta3_behavioral_drift_penalty": 0.2350903036865047,
        "high_escalation_threshold": 0.6471690880318302,
        "moderate_escalation_threshold": 0.35535958474237833,
        "low_credibility_threshold": 0.23004063420103887,
        "high_credibility_threshold": 0.6409166321079935,
        "credibility_penalty_weight": 0.10472134132049038,
        "trajectory_penalty_weight": 0.14519592501753803,
        "eta_optimization_factor": 1.1020064337854993,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.5480172553268904,
          0.324210270014715,
          0.12777247465839453
        ]
      },
      "f1_score": 0.7285714285714285,
      "f2_score": 0.0,
      "f3_score": 0.030612244897959183,
      "overall_score": 0.41008962167056173,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.0915523252443949,
        "w2_progressive_weight": 0.4062306858417988,
        "w3_clarification_weight": 0.5022169889138064,
        "gamma": 0.938647574473463,
        "beta": 0.5719263211382244,
        "reject_threshold": 0.8811682222359151,
        "clarify_threshold": 0.48370619663253545,
        "delta1_evasion_penalty": 0.12936678505497262,
        "delta2_single_attack_penalty": 0.23936414129810135,
        "delta3_behavioral_drift_penalty": 0.2899709110101216,
        "high_escalation_threshold": 0.6454556260441459,
        "moderate_escalation_threshold": 0.42839874940758105,
        "low_credibility_threshold": 0.32566061153534476,
        "high_credibility_threshold": 0.61642428801411,
        "credibility_penalty_weight": 0.1769926715237704,
        "trajectory_penalty_weight": 0.1428615419226557,
        "eta_optimization_factor": 1.09496349470174,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6196645960316385,
          0.13312818170363538,
          0.24720722226472616
        ]
      },
      "f1_score": 0.673469387755102,
      "f2_score": 0.0,
      "f3_score": 0.10714285714285714,
      "overall_score": 0.11546665152780641,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.4822526843975189,
        "w2_progressive_weight": 0.3146325729164695,
        "w3_clarification_weight": 0.2031147426860116,
        "gamma": 0.7622310886531041,
        "beta": 0.5674506858291899,
        "reject_threshold": 0.8126447688590281,
        "clarify_threshold": 0.47087927079690917,
        "delta1_evasion_penalty": 0.25655621298531367,
        "delta2_single_attack_penalty": 0.42839524976557153,
        "delta3_behavioral_drift_penalty": 0.09365639772836494,
        "high_escalation_threshold": 0.6307745754103327,
        "moderate_escalation_threshold": 0.4377308533165821,
        "low_credibility_threshold": 0.24611502945426147,
        "high_credibility_threshold": 0.7081126753557709,
        "credibility_penalty_weight": 0.1753864903627582,
        "trajectory_penalty_weight": 0.1444727480873257,
        "eta_optimization_factor": 1.0430263196327958,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.34643173515641024,
          0.5325888058164799,
          0.12097945902710988
        ]
      },
      "f1_score": 0.6275510204081634,
      "f2_score": 0.0,
      "f3_score": 0.16326530612244897,
      "overall_score": 0.33579975483085306,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.5013753087049512,
        "w2_progressive_weight": 0.32203680088438347,
        "w3_clarification_weight": 0.17658789041066544,
        "gamma": 0.8427604939163488,
        "beta": 0.30313490251665237,
        "reject_threshold": 0.7892235583243247,
        "clarify_threshold": 0.33368238648011506,
        "delta1_evasion_penalty": 0.17688619019121427,
        "delta2_single_attack_penalty": 0.37603358522928443,
        "delta3_behavioral_drift_penalty": 0.06291150086218837,
        "high_escalation_threshold": 0.5464020121007298,
        "moderate_escalation_threshold": 0.3013373079316643,
        "low_credibility_threshold": 0.34207738762532014,
        "high_credibility_threshold": 0.7010493797748573,
        "credibility_penalty_weight": 0.12969116377763587,
        "trajectory_penalty_weight": 0.06713817154493738,
        "eta_optimization_factor": 1.0874629606191422,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.4759661382079709,
          0.3127670994426579,
          0.21126676234937128
        ]
      },
      "f1_score": 0.47857142857142854,
      "f2_score": 0.0,
      "f3_score": 0.4030612244897959,
      "overall_score": 0.31111962907636215,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.657150274951716,
        "w2_progressive_weight": 0.23658530940006775,
        "w3_clarification_weight": 0.10626441564821627,
        "gamma": 0.6261004433334068,
        "beta": 0.6708070559389503,
        "reject_threshold": 0.7796082929029878,
        "clarify_threshold": 0.6796082929029879,
        "delta1_evasion_penalty": 0.324309182173655,
        "delta2_single_attack_penalty": 0.20323388619122032,
        "delta3_behavioral_drift_penalty": 0.240987448004434,
        "high_escalation_threshold": 0.6653045329752098,
        "moderate_escalation_threshold": 0.5653045329752098,
        "low_credibility_threshold": 0.34324527466035837,
        "high_credibility_threshold": 0.6242220205702959,
        "credibility_penalty_weight": 0.14805141218798712,
        "trajectory_penalty_weight": 0.15113182217558763,
        "eta_optimization_factor": 0.9184842467908719,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6324039007680181,
          0.17058067266998309,
          0.19701542656199877
        ]
      },
      "f1_score": 0.6489795918367346,
      "f2_score": 0.0,
      "f3_score": 0.11734693877551021,
      "overall_score": 0.43894692109064914,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.2670297666029529,
        "w2_progressive_weight": 0.3770485471240846,
        "w3_clarification_weight": 0.35592168627296256,
        "gamma": 0.6815209484171079,
        "beta": 0.5957406954958003,
        "reject_threshold": 0.7345217567290713,
        "clarify_threshold": 0.6345217567290713,
        "delta1_evasion_penalty": 0.1971261652956065,
        "delta2_single_attack_penalty": 0.18654578254375528,
        "delta3_behavioral_drift_penalty": 0.22247080999050894,
        "high_escalation_threshold": 0.5023759845724325,
        "moderate_escalation_threshold": 0.46743548689701064,
        "low_credibility_threshold": 0.3886405692486732,
        "high_credibility_threshold": 0.646284359358642,
        "credibility_penalty_weight": 0.21202704093435576,
        "trajectory_penalty_weight": 0.18031001573412064,
        "eta_optimization_factor": 0.9035207407370894,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.22942430853193016,
          0.5264026415348602,
          0.24417304993320962
        ]
      },
      "f1_score": 0.6816326530612246,
      "f2_score": 0.0,
      "f3_score": 0.09183673469387756,
      "overall_score": 0.21470289372993795,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.14830457345515397,
        "w2_progressive_weight": 0.4910445011275274,
        "w3_clarification_weight": 0.3606509254173186,
        "gamma": 0.7998277856225579,
        "beta": 0.5045719478260267,
        "reject_threshold": 0.6664004197356121,
        "clarify_threshold": 0.590282900442335,
        "delta1_evasion_penalty": 0.36142934028428775,
        "delta2_single_attack_penalty": 0.3132761130486286,
        "delta3_behavioral_drift_penalty": 0.10441470385693886,
        "high_escalation_threshold": 0.5785495641048378,
        "moderate_escalation_threshold": 0.31607353764118123,
        "low_credibility_threshold": 0.389995142698484,
        "high_credibility_threshold": 0.5989272331268892,
        "credibility_penalty_weight": 0.24495289981637303,
        "trajectory_penalty_weight": 0.2158887929243477,
        "eta_optimization_factor": 0.989398078166817,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.5786846033588183,
          0.3494273090645615,
          0.07188808757662023
        ]
      },
      "f1_score": 0.7428571428571429,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.11200916745146805,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.20490404700763235,
        "w2_progressive_weight": 0.3823084942765504,
        "w3_clarification_weight": 0.4127874587158173,
        "gamma": 0.756727788143263,
        "beta": 0.31044891352878834,
        "reject_threshold": 0.759088536481398,
        "clarify_threshold": 0.5097103492151598,
        "delta1_evasion_penalty": 0.39229214130049017,
        "delta2_single_attack_penalty": 0.29764225066500816,
        "delta3_behavioral_drift_penalty": 0.056844598392307724,
        "high_escalation_threshold": 0.664691442627045,
        "moderate_escalation_threshold": 0.30793338190475633,
        "low_credibility_threshold": 0.37762084687578534,
        "high_credibility_threshold": 0.770312829789859,
        "credibility_penalty_weight": 0.26902088762898324,
        "trajectory_penalty_weight": 0.16601500905816913,
        "eta_optimization_factor": 1.1407264387662261,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.4692391756321278,
          0.26164660786037286,
          0.26911421650749934
        ]
      },
      "f1_score": 0.4714285714285714,
      "f2_score": 0.0,
      "f3_score": 0.3622448979591837,
      "overall_score": 0.24612777302208294,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.46076617464746483,
        "w2_progressive_weight": 0.17002762007529637,
        "w3_clarification_weight": 0.36920620527723874,
        "gamma": 0.7718644731902757,
        "beta": 0.6267046128907814,
        "reject_threshold": 0.8075302560976414,
        "clarify_threshold": 0.5422932652581378,
        "delta1_evasion_penalty": 0.22849903203317778,
        "delta2_single_attack_penalty": 0.3455337162434221,
        "delta3_behavioral_drift_penalty": 0.22436567928360523,
        "high_escalation_threshold": 0.695375019001841,
        "moderate_escalation_threshold": 0.5466312141059588,
        "low_credibility_threshold": 0.3014626374144372,
        "high_credibility_threshold": 0.5015694938494842,
        "credibility_penalty_weight": 0.2840836376192043,
        "trajectory_penalty_weight": 0.18395074995805427,
        "eta_optimization_factor": 1.0956524680933755,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.31571526190916654,
          0.5411889640250017,
          0.1430957740658317
        ]
      },
      "f1_score": 0.6928571428571428,
      "f2_score": 0.0,
      "f3_score": 0.07653061224489796,
      "overall_score": 0.3475007122259403,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3762889054192726,
        "w2_progressive_weight": 0.43393691557416375,
        "w3_clarification_weight": 0.18977417900656354,
        "gamma": 0.761637449369458,
        "beta": 0.6119893830898583,
        "reject_threshold": 0.7067808206594932,
        "clarify_threshold": 0.6440615884358163,
        "delta1_evasion_penalty": 0.39443020033373466,
        "delta2_single_attack_penalty": 0.3811052214327634,
        "delta3_behavioral_drift_penalty": 0.11624353966932123,
        "high_escalation_threshold": 0.5983053987314937,
        "moderate_escalation_threshold": 0.4398869796459891,
        "low_credibility_threshold": 0.2852526329560003,
        "high_credibility_threshold": 0.5888756980935664,
        "credibility_penalty_weight": 0.2961607154689896,
        "trajectory_penalty_weight": 0.14382833630922687,
        "eta_optimization_factor": 1.0768078980855553,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6207382253517965,
          0.24504079627095704,
          0.1342209783772465
        ]
      },
      "f1_score": 0.7428571428571429,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.28049713677577887,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.37457859234101837,
        "w2_progressive_weight": 0.16792927546432562,
        "w3_clarification_weight": 0.45749213219465606,
        "gamma": 0.8389649872187408,
        "beta": 0.6165248850651912,
        "reject_threshold": 0.6862904899675276,
        "clarify_threshold": 0.370183797786906,
        "delta1_evasion_penalty": 0.38627101212404,
        "delta2_single_attack_penalty": 0.4758799140710812,
        "delta3_behavioral_drift_penalty": 0.11478855143331405,
        "high_escalation_threshold": 0.7328645142469112,
        "moderate_escalation_threshold": 0.3890989632763836,
        "low_credibility_threshold": 0.2851783064149736,
        "high_credibility_threshold": 0.5158863384521212,
        "credibility_penalty_weight": 0.2541607084708325,
        "trajectory_penalty_weight": 0.16305282158866433,
        "eta_optimization_factor": 1.149493920532981,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.3447620571514939,
          0.46267608192965154,
          0.1925618609188545
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.28250364168055075,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3604810138078571,
        "w2_progressive_weight": 0.328253092724089,
        "w3_clarification_weight": 0.31126589346805383,
        "gamma": 0.8007358682331198,
        "beta": 0.7548184459121279,
        "reject_threshold": 0.7956097080488362,
        "clarify_threshold": 0.5583493263706099,
        "delta1_evasion_penalty": 0.17302774250826308,
        "delta2_single_attack_penalty": 0.19928819540430265,
        "delta3_behavioral_drift_penalty": 0.2842676621657721,
        "high_escalation_threshold": 0.757318760180782,
        "moderate_escalation_threshold": 0.30092550192264345,
        "low_credibility_threshold": 0.3239636213147228,
        "high_credibility_threshold": 0.565116854568468,
        "credibility_penalty_weight": 0.21761013980960126,
        "trajectory_penalty_weight": 0.2219983881747657,
        "eta_optimization_factor": 0.9795080252339976,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6597058178051929,
          0.13625703211698484,
          0.20403715007782222
        ]
      },
      "f1_score": 0.7438775510204083,
      "f2_score": 0.0,
      "f3_score": 0.01020408163265306,
      "overall_score": 0.2713299163271514,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.22761640756649285,
        "w2_progressive_weight": 0.29338640768828794,
        "w3_clarification_weight": 0.47899718474521913,
        "gamma": 0.5139926972247226,
        "beta": 0.7496092859401122,
        "reject_threshold": 0.7680497767037603,
        "clarify_threshold": 0.4658045178875178,
        "delta1_evasion_penalty": 0.17195824266892407,
        "delta2_single_attack_penalty": 0.1845431544260453,
        "delta3_behavioral_drift_penalty": 0.2289296870481527,
        "high_escalation_threshold": 0.6363633347900925,
        "moderate_escalation_threshold": 0.5159194670609761,
        "low_credibility_threshold": 0.21214411390232749,
        "high_credibility_threshold": 0.7542593983721582,
        "credibility_penalty_weight": 0.27239277439719856,
        "trajectory_penalty_weight": 0.1431886919490386,
        "eta_optimization_factor": 1.1998879172896286,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.6404598777464521,
          0.2213526032611855,
          0.1381875189923624
        ]
      },
      "f1_score": 0.6275510204081634,
      "f2_score": 0.0,
      "f3_score": 0.15816326530612246,
      "overall_score": 0.21860066764173683,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.21497041231232575,
        "w2_progressive_weight": 0.11469467285829185,
        "w3_clarification_weight": 0.6703349148293823,
        "gamma": 0.7653292101929172,
        "beta": 0.5224301488702884,
        "reject_threshold": 0.6998659434722807,
        "clarify_threshold": 0.3295084494529544,
        "delta1_evasion_penalty": 0.1832974745877487,
        "delta2_single_attack_penalty": 0.39000262192032464,
        "delta3_behavioral_drift_penalty": 0.28269900552497523,
        "high_escalation_threshold": 0.6982309484973905,
        "moderate_escalation_threshold": 0.32438777030509125,
        "low_credibility_threshold": 0.237403968450659,
        "high_credibility_threshold": 0.669206063336363,
        "credibility_penalty_weight": 0.2867029503607439,
        "trajectory_penalty_weight": 0.09215210915152805,
        "eta_optimization_factor": 1.1860018396834273,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.307415983821911,
          0.401670789612664,
          0.290913226565425
        ]
      },
      "f1_score": 0.7183673469387755,
      "f2_score": 0.0,
      "f3_score": 0.04081632653061224,
      "overall_score": 0.18178833353168633,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3964143564703914,
        "w2_progressive_weight": 0.3694604685165626,
        "w3_clarification_weight": 0.23412517501304603,
        "gamma": 0.8983781309276031,
        "beta": 0.782561539480582,
        "reject_threshold": 0.7940127723674841,
        "clarify_threshold": 0.6940127723674842,
        "delta1_evasion_penalty": 0.14510184704084775,
        "delta2_single_attack_penalty": 0.18967394213088157,
        "delta3_behavioral_drift_penalty": 0.25514612923542257,
        "high_escalation_threshold": 0.7164446023164754,
        "moderate_escalation_threshold": 0.48478997863163803,
        "low_credibility_threshold": 0.3676727078709824,
        "high_credibility_threshold": 0.7214063616203921,
        "credibility_penalty_weight": 0.21704421376418442,
        "trajectory_penalty_weight": 0.18498219073978261,
        "eta_optimization_factor": 0.8768829174000496,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.354880913885349,
          0.31427586703832655,
          0.3308432190763245
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.2976962746610838,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.5153551384871594,
        "w2_progressive_weight": 0.2655437655361173,
        "w3_clarification_weight": 0.21910109597672334,
        "gamma": 0.8786955070007336,
        "beta": 0.33697365751069175,
        "reject_threshold": 0.867095107522299,
        "clarify_threshold": 0.6761515407301358,
        "delta1_evasion_penalty": 0.3320444499574511,
        "delta2_single_attack_penalty": 0.49845246369187646,
        "delta3_behavioral_drift_penalty": 0.0649370331335087,
        "high_escalation_threshold": 0.6834920189714562,
        "moderate_escalation_threshold": 0.5076125578661845,
        "low_credibility_threshold": 0.2756566616061865,
        "high_credibility_threshold": 0.7410367222022309,
        "credibility_penalty_weight": 0.17104928660763047,
        "trajectory_penalty_weight": 0.0930046247770831,
        "eta_optimization_factor": 0.9122388360350776,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.5554254428519587,
          0.21495663439545484,
          0.22961792275258638
        ]
      },
      "f1_score": 0.4581632653061224,
      "f2_score": 0.0,
      "f3_score": 0.3163265306122449,
      "overall_score": 0.30542428258522325,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.25088148791371323,
        "w2_progressive_weight": 0.23380932779149477,
        "w3_clarification_weight": 0.515309184294792,
        "gamma": 0.9428219770746533,
        "beta": 0.30830726946839565,
        "reject_threshold": 0.7792754518194491,
        "clarify_threshold": 0.6231670725058184,
        "delta1_evasion_penalty": 0.25522288661467285,
        "delta2_single_attack_penalty": 0.3096092451353961,
        "delta3_behavioral_drift_penalty": 0.12257221809358644,
        "high_escalation_threshold": 0.6804059066005372,
        "moderate_escalation_threshold": 0.43072966585845796,
        "low_credibility_threshold": 0.3493210296095779,
        "high_credibility_threshold": 0.6074316572611269,
        "credibility_penalty_weight": 0.2322539261209751,
        "trajectory_penalty_weight": 0.10293670738183805,
        "eta_optimization_factor": 0.9047190841483798,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.5132873913994278,
          0.2865304399968836,
          0.20018216860368848
        ]
      },
      "f1_score": 0.6051020408163266,
      "f2_score": 0.0,
      "f3_score": 0.17857142857142858,
      "overall_score": 0.243828397535123,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3408318563565694,
        "w2_progressive_weight": 0.575875361061407,
        "w3_clarification_weight": 0.08329278258202369,
        "gamma": 0.9385744949984826,
        "beta": 0.33884218687529166,
        "reject_threshold": 0.6615153780184451,
        "clarify_threshold": 0.47829347759758317,
        "delta1_evasion_penalty": 0.37377086899278833,
        "delta2_single_attack_penalty": 0.2272386628262574,
        "delta3_behavioral_drift_penalty": 0.27435348986465363,
        "high_escalation_threshold": 0.6898650168925587,
        "moderate_escalation_threshold": 0.5587115287969684,
        "low_credibility_threshold": 0.3436729997934761,
        "high_credibility_threshold": 0.7881887795831484,
        "credibility_penalty_weight": 0.11664087207235954,
        "trajectory_penalty_weight": 0.24367738627436858,
        "eta_optimization_factor": 0.8427963404632782,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6292877354081499,
          0.2002883670035367,
          0.1704238975883134
        ]
      },
      "f1_score": 0.7438775510204083,
      "f2_score": 0.0,
      "f3_score": 0.01020408163265306,
      "overall_score": 0.2543870929691422,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.47812586994661577,
        "w2_progressive_weight": 0.3531559979048638,
        "w3_clarification_weight": 0.16871813214852058,
        "gamma": 0.6255681941460853,
        "beta": 0.7148260438432088,
        "reject_threshold": 0.6960168199966015,
        "clarify_threshold": 0.5599956348586239,
        "delta1_evasion_penalty": 0.34061957460634906,
        "delta2_single_attack_penalty": 0.3010824981233365,
        "delta3_behavioral_drift_penalty": 0.20915976666842584,
        "high_escalation_threshold": 0.5798235994477696,
        "moderate_escalation_threshold": 0.3555908173953327,
        "low_credibility_threshold": 0.24532696922872765,
        "high_credibility_threshold": 0.7238865698164789,
        "credibility_penalty_weight": 0.2477257876484334,
        "trajectory_penalty_weight": 0.0800160486652331,
        "eta_optimization_factor": 0.91585005648244,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.36287121030372504,
          0.4181853390647357,
          0.21894345063153922
        ]
      },
      "f1_score": 0.7316326530612244,
      "f2_score": 0.0,
      "f3_score": 0.02040816326530612,
      "overall_score": 0.3532557259129529,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.08052906391116875,
        "w2_progressive_weight": 0.4352751636671397,
        "w3_clarification_weight": 0.48419577242169165,
        "gamma": 0.7197349892489433,
        "beta": 0.5134958667477675,
        "reject_threshold": 0.8239470778991067,
        "clarify_threshold": 0.6339935170000441,
        "delta1_evasion_penalty": 0.15490273403233035,
        "delta2_single_attack_penalty": 0.31435390349908265,
        "delta3_behavioral_drift_penalty": 0.0871102299189709,
        "high_escalation_threshold": 0.6014428298189095,
        "moderate_escalation_threshold": 0.4018922479553197,
        "low_credibility_threshold": 0.36746624493095936,
        "high_credibility_threshold": 0.6090051696364169,
        "credibility_penalty_weight": 0.20744076644599552,
        "trajectory_penalty_weight": 0.20813155437343583,
        "eta_optimization_factor": 0.9686941714903464,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.43691607056593074,
          0.38107321732364446,
          0.18201071211042477
        ]
      },
      "f1_score": 0.5673469387755101,
      "f2_score": 0.0,
      "f3_score": 0.25,
      "overall_score": 0.1667368609978819,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.34569746926299627,
        "w2_progressive_weight": 0.45667357286382076,
        "w3_clarification_weight": 0.1976289578731831,
        "gamma": 0.8122094595661722,
        "beta": 0.7370142549309189,
        "reject_threshold": 0.8945027287456545,
        "clarify_threshold": 0.46402283283981155,
        "delta1_evasion_penalty": 0.33828040052829755,
        "delta2_single_attack_penalty": 0.27761318752472997,
        "delta3_behavioral_drift_penalty": 0.1088465426996728,
        "high_escalation_threshold": 0.7256796055182211,
        "moderate_escalation_threshold": 0.5340385476001418,
        "low_credibility_threshold": 0.32170941115260737,
        "high_credibility_threshold": 0.6305212161721949,
        "credibility_penalty_weight": 0.262354908899484,
        "trajectory_penalty_weight": 0.14438656914794057,
        "eta_optimization_factor": 1.1096834321986155,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.3152290237375816,
          0.2850708688303191,
          0.39970010743209927
        ]
      },
      "f1_score": 0.6908163265306123,
      "f2_score": 0.0,
      "f3_score": 0.08163265306122448,
      "overall_score": 0.25494643196010525,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.44149497287532674,
        "w2_progressive_weight": 0.12854136581440537,
        "w3_clarification_weight": 0.4299636613102679,
        "gamma": 0.9465698573111401,
        "beta": 0.5543633387081006,
        "reject_threshold": 0.8354290142654879,
        "clarify_threshold": 0.6444450202527329,
        "delta1_evasion_penalty": 0.3687262996929608,
        "delta2_single_attack_penalty": 0.15835495701345406,
        "delta3_behavioral_drift_penalty": 0.1181998068952687,
        "high_escalation_threshold": 0.6012643614090911,
        "moderate_escalation_threshold": 0.5236949631661331,
        "low_credibility_threshold": 0.2520617372816815,
        "high_credibility_threshold": 0.7141497091230538,
        "credibility_penalty_weight": 0.28523037880387336,
        "trajectory_penalty_weight": 0.12936411018870278,
        "eta_optimization_factor": 0.997482836382478,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.45708493897445085,
          0.3677103583383977,
          0.17520470268715158
        ]
      },
      "f1_score": 0.6948979591836735,
      "f2_score": 0.0,
      "f3_score": 0.0663265306122449,
      "overall_score": 0.33531195358496424,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3684337025954248,
        "w2_progressive_weight": 0.15167751535231822,
        "w3_clarification_weight": 0.47988878205225693,
        "gamma": 0.6633485366265159,
        "beta": 0.5928354299537191,
        "reject_threshold": 0.8752643753791177,
        "clarify_threshold": 0.37861876097427793,
        "delta1_evasion_penalty": 0.34201289317013106,
        "delta2_single_attack_penalty": 0.46050894769875095,
        "delta3_behavioral_drift_penalty": 0.0863985509111604,
        "high_escalation_threshold": 0.7407233128174857,
        "moderate_escalation_threshold": 0.515912452517395,
        "low_credibility_threshold": 0.21563163766905968,
        "high_credibility_threshold": 0.5502587580116296,
        "credibility_penalty_weight": 0.21492162701362139,
        "trajectory_penalty_weight": 0.09277574188781056,
        "eta_optimization_factor": 0.8360839705446065,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.6083845432031235,
          0.19000104179190633,
          0.2016144150049701
        ]
      },
      "f1_score": 0.5448979591836735,
      "f2_score": 0.0,
      "f3_score": 0.29081632653061223,
      "overall_score": 0.3403182653784184,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3422199320339238,
        "w2_progressive_weight": 0.2969054605892045,
        "w3_clarification_weight": 0.3608746073768717,
        "gamma": 0.7102830206299892,
        "beta": 0.7323856461776845,
        "reject_threshold": 0.63549516691448,
        "clarify_threshold": 0.32372482834561234,
        "delta1_evasion_penalty": 0.32382503867975054,
        "delta2_single_attack_penalty": 0.20912462580687852,
        "delta3_behavioral_drift_penalty": 0.20657208720520415,
        "high_escalation_threshold": 0.7805343828631386,
        "moderate_escalation_threshold": 0.323033887112377,
        "low_credibility_threshold": 0.3964117181371414,
        "high_credibility_threshold": 0.7083410076904184,
        "credibility_penalty_weight": 0.16935794490391626,
        "trajectory_penalty_weight": 0.17491545436820172,
        "eta_optimization_factor": 1.0547422810632403,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.2172969923764711,
          0.394026366389725,
          0.38867664123380397
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.25780773797729645,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.42355113110716713,
        "w2_progressive_weight": 0.3072410798199381,
        "w3_clarification_weight": 0.26920778907289483,
        "gamma": 0.5635282687247725,
        "beta": 0.3192989856906988,
        "reject_threshold": 0.7826687394727051,
        "clarify_threshold": 0.36803587066468996,
        "delta1_evasion_penalty": 0.25495336401608615,
        "delta2_single_attack_penalty": 0.13559006321366263,
        "delta3_behavioral_drift_penalty": 0.2787886591595476,
        "high_escalation_threshold": 0.6794520697112884,
        "moderate_escalation_threshold": 0.5794520697112884,
        "low_credibility_threshold": 0.23635376383254592,
        "high_credibility_threshold": 0.6634075957502789,
        "credibility_penalty_weight": 0.2926346978991179,
        "trajectory_penalty_weight": 0.10408994953293733,
        "eta_optimization_factor": 0.8327107103614743,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.5938206538615363,
          0.31799268980492224,
          0.08818665633354152
        ]
      },
      "f1_score": 0.3520408163265306,
      "f2_score": 0.01326530612244898,
      "f3_score": 0.5663265306122449,
      "overall_score": 0.3056424461276413,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.11549181948168637,
        "w2_progressive_weight": 0.16675867352230997,
        "w3_clarification_weight": 0.7177495069960037,
        "gamma": 0.7090310777248352,
        "beta": 0.3164346591459321,
        "reject_threshold": 0.7700080917836853,
        "clarify_threshold": 0.5772341935617833,
        "delta1_evasion_penalty": 0.1531271835054135,
        "delta2_single_attack_penalty": 0.23840212896085214,
        "delta3_behavioral_drift_penalty": 0.22585607151304432,
        "high_escalation_threshold": 0.6133352690560107,
        "moderate_escalation_threshold": 0.35477805619096153,
        "low_credibility_threshold": 0.28691496300707686,
        "high_credibility_threshold": 0.6073649366217518,
        "credibility_penalty_weight": 0.14935393135062425,
        "trajectory_penalty_weight": 0.18481965128642336,
        "eta_optimization_factor": 1.0856585399222296,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.6030116085066388,
          0.24069609267675676,
          0.1562922988166044
        ]
      },
      "f1_score": 0.3908163265306122,
      "f2_score": 0.0,
      "f3_score": 0.3877551020408163,
      "overall_score": 0.32344712195915026,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.3046799035592945,
        "w2_progressive_weight": 0.20405835594806512,
        "w3_clarification_weight": 0.49126174049264043,
        "gamma": 0.7044093456517555,
        "beta": 0.6386603458984232,
        "reject_threshold": 0.7002802917973585,
        "clarify_threshold": 0.43086142703791264,
        "delta1_evasion_penalty": 0.3115813345965355,
        "delta2_single_attack_penalty": 0.19161227154197596,
        "delta3_behavioral_drift_penalty": 0.1816595838446416,
        "high_escalation_threshold": 0.6917674775062809,
        "moderate_escalation_threshold": 0.5917674775062809,
        "low_credibility_threshold": 0.2112553731499308,
        "high_credibility_threshold": 0.7332700029081741,
        "credibility_penalty_weight": 0.29327971393300434,
        "trajectory_penalty_weight": 0.0734055670369507,
        "eta_optimization_factor": 1.0615801795204978,
        "risk_lock_threshold": 3,
        "omega_weights": [
          0.47636282787040285,
          0.36890044863055366,
          0.15473672349904344
        ]
      },
      "f1_score": 0.7387755102040817,
      "f2_score": 0.0,
      "f3_score": 0.015306122448979591,
      "overall_score": 0.23260936355542738,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.12767355762167285,
        "w2_progressive_weight": 0.2398117791170134,
        "w3_clarification_weight": 0.6325146632613138,
        "gamma": 0.5063272180194619,
        "beta": 0.66067598136088,
        "reject_threshold": 0.625557255854573,
        "clarify_threshold": 0.41097659422722177,
        "delta1_evasion_penalty": 0.15747135671204412,
        "delta2_single_attack_penalty": 0.1253414461785301,
        "delta3_behavioral_drift_penalty": 0.19233270941873082,
        "high_escalation_threshold": 0.796072366087931,
        "moderate_escalation_threshold": 0.4649077122339651,
        "low_credibility_threshold": 0.3529417050955568,
        "high_credibility_threshold": 0.7738210020298262,
        "credibility_penalty_weight": 0.24948906805394144,
        "trajectory_penalty_weight": 0.06215693435081986,
        "eta_optimization_factor": 1.1401812659883959,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.7764265156957164,
          0.13807437170931933,
          0.08549911259496434
        ]
      },
      "f1_score": 0.7285714285714285,
      "f2_score": 0.0,
      "f3_score": 0.030612244897959183,
      "overall_score": 0.11238200004052432,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.42174931230919144,
        "w2_progressive_weight": 0.09559775681153208,
        "w3_clarification_weight": 0.4826529308792765,
        "gamma": 0.8163320373758928,
        "beta": 0.7445752182312317,
        "reject_threshold": 0.7775386329824874,
        "clarify_threshold": 0.5029216198249962,
        "delta1_evasion_penalty": 0.3328881109972518,
        "delta2_single_attack_penalty": 0.1900289072242722,
        "delta3_behavioral_drift_penalty": 0.26309780446023995,
        "high_escalation_threshold": 0.7342127007337078,
        "moderate_escalation_threshold": 0.5918235784430291,
        "low_credibility_threshold": 0.26130500992038086,
        "high_credibility_threshold": 0.7888639016665628,
        "credibility_penalty_weight": 0.14969279901929594,
        "trajectory_penalty_weight": 0.12033572545855634,
        "eta_optimization_factor": 0.8325679371974981,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.6430521352147389,
          0.28426121222597955,
          0.07268665255928149
        ]
      },
      "f1_score": 0.7479591836734694,
      "f2_score": 0.0,
      "f3_score": 0.00510204081632653,
      "overall_score": 0.31791378630309564,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.33305067540381966,
        "w2_progressive_weight": 0.25054145952449136,
        "w3_clarification_weight": 0.41640786507168903,
        "gamma": 0.5217728655232802,
        "beta": 0.7051210284894052,
        "reject_threshold": 0.8988445667368019,
        "clarify_threshold": 0.44107933428377954,
        "delta1_evasion_penalty": 0.2579666569166037,
        "delta2_single_attack_penalty": 0.20592250839984377,
        "delta3_behavioral_drift_penalty": 0.26068734992154446,
        "high_escalation_threshold": 0.5789646840783318,
        "moderate_escalation_threshold": 0.5600986784818721,
        "low_credibility_threshold": 0.322327394549943,
        "high_credibility_threshold": 0.558902156779171,
        "credibility_penalty_weight": 0.1520613787007501,
        "trajectory_penalty_weight": 0.14324598625056345,
        "eta_optimization_factor": 1.097327883359814,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.5299508906962213,
          0.3621914494687891,
          0.10785765983498964
        ]
      },
      "f1_score": 0.5081632653061224,
      "f2_score": 0.00510204081632653,
      "f3_score": 0.3469387755102041,
      "overall_score": 0.31499042629908053,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.29972229242479487,
        "w2_progressive_weight": 0.3046877929897413,
        "w3_clarification_weight": 0.3955899145854637,
        "gamma": 0.7328872071616896,
        "beta": 0.43703551915550504,
        "reject_threshold": 0.7794912816850714,
        "clarify_threshold": 0.573414874217048,
        "delta1_evasion_penalty": 0.22181997924697663,
        "delta2_single_attack_penalty": 0.36640560013197454,
        "delta3_behavioral_drift_penalty": 0.16153396438713125,
        "high_escalation_threshold": 0.7307860464296717,
        "moderate_escalation_threshold": 0.545296803482842,
        "low_credibility_threshold": 0.21464807642981956,
        "high_credibility_threshold": 0.5328602295917835,
        "credibility_penalty_weight": 0.2320536737478305,
        "trajectory_penalty_weight": 0.05602092891763544,
        "eta_optimization_factor": 1.0203918077908891,
        "risk_lock_threshold": 4,
        "omega_weights": [
          0.698709268746431,
          0.08859410360572553,
          0.21269662764784353
        ]
      },
      "f1_score": 0.576530612244898,
      "f2_score": 0.0,
      "f3_score": 0.2295918367346939,
      "overall_score": 0.26362329183850863,
      "iteration": 0
    },
    {
      "params": {
        "w1_dangerous_weight": 0.27942449835697264,
        "w2_progressive_weight": 0.3208381339386626,
        "w3_clarification_weight": 0.39973736770436463,
        "gamma": 0.5621640334621975,
        "beta": 0.6721260106815561,
        "reject_threshold": 0.7309182535679003,
        "clarify_threshold": 0.5502650662454069,
        "delta1_evasion_penalty": 0.28288635303455334,
        "delta2_single_attack_penalty": 0.25356540081291723,
        "delta3_behavioral_drift_penalty": 0.1452652143837953,
        "high_escalation_threshold": 0.5491836113113738,
        "moderate_escalation_threshold": 0.3065397253248086,
        "low_credibility_threshold": 0.36295137152750356,
        "high_credibility_threshold": 0.6066935079266654,
        "credibility_penalty_weight": 0.12627255271513105,
        "trajectory_penalty_weight": 0.058277509686685415,
        "eta_optimization_factor": 0.8368439648206476,
        "risk_lock_threshold": 2,
        "omega_weights": [
          0.5686839426244501,
          0.2930592489668051,
          0.1382568084087449
        ]
      },
      "f1_score": 0.6316326530612245,
      "f2_score": 0.0,
      "f3_score": 0.15816326530612246,
      "overall_score": 0.23971740456851287,
      "iteration": 0
    }
  ],
  "training_history": [
    [
      